#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人员部门关联提取工具
从HR_USER.csv和HR_DEPARTMENT.csv中提取人员与部门的对应关系
输出格式: 人员姓名,部门名称
"""

import csv
import sys
from collections import defaultdict

def read_departments(dept_file):
    """
    读取部门数据，建立ID到部门名称的映射
    
    Args:
        dept_file: 部门CSV文件路径
        
    Returns:
        dict: {部门ID: 部门名称}
    """
    dept_mapping = {}
    missing_names = 0
    
    print(f"正在读取部门数据: {dept_file}")
    
    try:
        with open(dept_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for row_num, row in enumerate(reader, start=2):  # 从第2行开始计数（跳过标题行）
                dept_id = row.get('ID', '').strip()
                dept_name = row.get('STR_NAME', '').strip()
                
                if dept_id:
                    if dept_name:
                        dept_mapping[dept_id] = dept_name
                    else:
                        missing_names += 1
                        print(f"警告: 第{row_num}行部门ID [{dept_id}] 缺少部门名称")
                        dept_mapping[dept_id] = f"未知部门({dept_id[:8]}...)"
                        
    except FileNotFoundError:
        print(f"错误: 找不到部门文件 {dept_file}")
        sys.exit(1)
    except Exception as e:
        print(f"错误: 读取部门文件时发生异常 - {e}")
        sys.exit(1)
    
    print(f"部门数据读取完成: 共{len(dept_mapping)}个部门")
    if missing_names > 0:
        print(f"警告: 有{missing_names}个部门缺少名称")
    
    return dept_mapping

def process_users_and_departments(user_file, dept_mapping, output_file):
    """
    处理用户数据，关联部门信息并输出
    
    Args:
        user_file: 用户CSV文件路径
        dept_mapping: 部门ID到名称的映射字典
        output_file: 输出CSV文件路径
    """
    print(f"正在处理用户数据: {user_file}")
    
    # 统计信息
    total_users = 0
    matched_users = 0
    missing_dept_users = 0
    empty_dept_users = 0
    
    # 记录未找到的部门ID
    missing_dept_ids = defaultdict(int)
    
    try:
        with open(user_file, 'r', encoding='utf-8') as input_f, \
             open(output_file, 'w', encoding='utf-8', newline='') as output_f:
            
            reader = csv.DictReader(input_f)
            writer = csv.writer(output_f)
            
            # 写入标题行
            writer.writerow(['人员姓名', '部门名称', '用户工号', '手机号码', '部门ID'])
            
            for row_num, row in enumerate(reader, start=2):
                total_users += 1
                
                # 获取用户信息
                user_name = row.get('NAME', '').strip()
                user_no = row.get('USER_NO', '').strip()
                phone = row.get('PHONE', '').strip()
                dept_id = row.get('DEPT_ID', '').strip()
                
                # 处理部门信息
                if not dept_id:
                    # 部门ID为空
                    dept_name = "未分配部门"
                    empty_dept_users += 1
                elif dept_id in dept_mapping:
                    # 找到对应部门
                    dept_name = dept_mapping[dept_id]
                    matched_users += 1
                else:
                    # 未找到对应部门
                    dept_name = f"未知部门({dept_id[:8]}...)"
                    missing_dept_users += 1
                    missing_dept_ids[dept_id] += 1
                
                # 写入结果
                writer.writerow([user_name, dept_name, user_no, phone, dept_id])
                
                # 进度显示
                if total_users % 1000 == 0:
                    print(f"已处理 {total_users} 个用户...")
                    
    except FileNotFoundError:
        print(f"错误: 找不到用户文件 {user_file}")
        sys.exit(1)
    except Exception as e:
        print(f"错误: 处理用户文件时发生异常 - {e}")
        sys.exit(1)
    
    # 输出统计信息
    print(f"\n=== 处理完成 ===")
    print(f"总用户数: {total_users}")
    print(f"成功匹配部门: {matched_users} ({matched_users/total_users*100:.1f}%)")
    print(f"部门ID为空: {empty_dept_users} ({empty_dept_users/total_users*100:.1f}%)")
    print(f"未找到部门: {missing_dept_users} ({missing_dept_users/total_users*100:.1f}%)")
    
    # 显示未找到的部门ID（前10个）
    if missing_dept_ids:
        print(f"\n未找到的部门ID (前10个):")
        for dept_id, count in list(missing_dept_ids.items())[:10]:
            print(f"  {dept_id}: {count}个用户")
        if len(missing_dept_ids) > 10:
            print(f"  ... 还有{len(missing_dept_ids)-10}个未找到的部门ID")
    
    print(f"\n结果已保存到: {output_file}")

def main():
    """主函数"""
    print("=== 人员部门关联提取工具 ===")
    
    # 文件路径
    dept_file = "HR_DEPARTMENT.csv"
    user_file = "HR_USER.csv"
    output_file = "用户部门对应关系.csv"
    
    print(f"部门文件: {dept_file}")
    print(f"用户文件: {user_file}")
    print(f"输出文件: {output_file}")
    print()
    
    # 步骤1: 读取部门数据
    dept_mapping = read_departments(dept_file)
    
    # 步骤2: 处理用户数据并输出
    process_users_and_departments(user_file, dept_mapping, output_file)
    
    print("\n=== 处理完成 ===")

if __name__ == "__main__":
    main()
