#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于工号的用户部门关联验证工具
使用工号(account)作为主要匹配字段，避免重名问题
"""

import csv
import re
from collections import defaultdict

def parse_sql_insert_statements(sql_file):
    """解析SQL文件中的INSERT语句"""
    records = []
    
    try:
        with open(sql_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        insert_pattern = r"INSERT INTO.*?VALUES\s*\((.*?)\);"
        matches = re.findall(insert_pattern, content, re.DOTALL | re.IGNORECASE)
        
        for match in matches:
            values = []
            current_value = ""
            in_quotes = False
            quote_char = None
            
            i = 0
            while i < len(match):
                char = match[i]
                
                if not in_quotes:
                    if char in ["'", '"']:
                        in_quotes = True
                        quote_char = char
                        current_value += char
                    elif char == ',':
                        values.append(current_value.strip())
                        current_value = ""
                    else:
                        current_value += char
                else:
                    current_value += char
                    if char == quote_char:
                        if i + 1 < len(match) and match[i + 1] == quote_char:
                            current_value += match[i + 1]
                            i += 1
                        else:
                            in_quotes = False
                            quote_char = None
                
                i += 1
            
            if current_value.strip():
                values.append(current_value.strip())
            
            records.append(values)
            
    except Exception as e:
        print(f"解析SQL文件时出错: {e}")
        return []
    
    return records

def extract_database_users_by_account(user_sql_file):
    """
    从t_user.sql中提取用户数据，以工号为键
    
    Returns:
        dict: {account: {'user_id': id, 'user_name': name, 'organ_affiliation': org_id}}
    """
    print("=== 提取数据库用户数据（按工号索引）===")
    
    records = parse_sql_insert_statements(user_sql_file)
    users_by_account = {}
    duplicate_accounts = defaultdict(list)
    
    for record in records:
        if len(record) >= 10:
            try:
                user_id = record[0].strip()
                user_name = record[1].strip().strip("'\"")
                is_del = record[2].strip().strip("'\"")
                origin_id = record[3].strip().strip("'\"")
                organ_affiliation = record[4].strip()
                account = record[5].strip().strip("'\"")
                
                # 只处理未删除的用户
                if is_del.lower() in ['f', 'false'] and account:
                    user_info = {
                        'user_id': user_id,
                        'user_name': user_name,
                        'organ_affiliation': organ_affiliation if organ_affiliation != 'NULL' else None,
                        'origin_id': origin_id if origin_id != 'NULL' else None
                    }
                    
                    if account in users_by_account:
                        # 记录重复的工号
                        if account not in duplicate_accounts:
                            duplicate_accounts[account].append(users_by_account[account])
                        duplicate_accounts[account].append(user_info)
                    else:
                        users_by_account[account] = user_info
                        
            except Exception as e:
                print(f"解析用户记录时出错: {e}")
                continue
    
    print(f"提取到 {len(users_by_account)} 个有效用户（按工号）")
    if duplicate_accounts:
        print(f"发现 {len(duplicate_accounts)} 个重复工号")
        print("重复工号示例（前5个）:")
        for i, (account, users) in enumerate(list(duplicate_accounts.items())[:5]):
            print(f"  工号 {account}: {len(users)}个用户 - {[u['user_name'] for u in users]}")
    
    return users_by_account, duplicate_accounts

def extract_database_orgs(org_sql_file):
    """从t_org_structure.sql中提取组织数据"""
    print("=== 提取数据库组织数据 ===")
    
    records = parse_sql_insert_statements(org_sql_file)
    orgs = {}
    
    for record in records:
        if len(record) >= 7:
            try:
                org_id = record[0].strip()
                organ_name = record[1].strip().strip("'\"")
                pre_id = record[2].strip()
                is_del = record[4].strip().strip("'\"")
                
                if is_del.lower() in ['f', 'false']:
                    orgs[org_id] = {
                        'organ_name': organ_name,
                        'pre_id': pre_id if pre_id != 'NULL' else None
                    }
            except Exception as e:
                continue
    
    print(f"提取到 {len(orgs)} 个有效组织")
    return orgs

def load_csv_mapping_by_account(csv_file):
    """
    加载CSV文件中的用户部门对应关系，以工号为键
    
    Returns:
        dict: {user_no: {'user_name': name, 'dept_name': name, 'dept_id': id}}
    """
    print("=== 加载CSV对应关系（按工号索引）===")
    
    csv_mapping = {}
    duplicate_user_nos = defaultdict(list)
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for row in reader:
                user_name = row.get('人员姓名', '').strip()
                dept_name = row.get('部门名称', '').strip()
                user_no = row.get('用户工号', '').strip()
                dept_id = row.get('部门ID', '').strip()
                
                if user_no and user_name:
                    user_info = {
                        'user_name': user_name,
                        'dept_name': dept_name,
                        'dept_id': dept_id
                    }
                    
                    if user_no in csv_mapping:
                        # 记录重复的工号
                        if user_no not in duplicate_user_nos:
                            duplicate_user_nos[user_no].append(csv_mapping[user_no])
                        duplicate_user_nos[user_no].append(user_info)
                    else:
                        csv_mapping[user_no] = user_info
                    
    except Exception as e:
        print(f"加载CSV文件时出错: {e}")
        return {}, {}
    
    print(f"加载到 {len(csv_mapping)} 个用户的对应关系（按工号）")
    if duplicate_user_nos:
        print(f"发现 {len(duplicate_user_nos)} 个重复工号")
        print("重复工号示例（前3个）:")
        for i, (user_no, users) in enumerate(list(duplicate_user_nos.items())[:3]):
            print(f"  工号 {user_no}: {[u['user_name'] for u in users]}")
    
    return csv_mapping, duplicate_user_nos

def validate_by_account(db_users, db_orgs, csv_mapping, db_duplicates, csv_duplicates):
    """
    基于工号验证数据库关联关系与CSV关联关系的一致性
    """
    print("=== 开始基于工号的验证 ===")
    
    # 统计信息
    total_csv_users = len(csv_mapping)
    exact_matched = 0  # 工号和部门都匹配
    account_matched = 0  # 工号匹配但部门不匹配
    account_not_found = 0  # CSV中的工号在数据库中找不到
    name_mismatch = 0  # 工号匹配但姓名不匹配
    
    # 详细结果
    validation_results = []
    
    print(f"开始验证 {total_csv_users} 个CSV用户...")
    
    for csv_account, csv_info in csv_mapping.items():
        result = {
            'csv_account': csv_account,
            'csv_user_name': csv_info['user_name'],
            'csv_dept_name': csv_info['dept_name'],
            'csv_dept_id': csv_info['dept_id'],
            'status': 'unknown',
            'db_user_name': None,
            'db_dept_name': None,
            'db_org_id': None,
            'name_match': False,
            'dept_match': False
        }
        
        # 检查工号是否在数据库中
        if csv_account in db_users:
            db_user_info = db_users[csv_account]
            result['db_user_name'] = db_user_info['user_name']
            result['db_org_id'] = db_user_info['organ_affiliation']
            
            # 检查姓名是否匹配
            if csv_info['user_name'] == db_user_info['user_name']:
                result['name_match'] = True
            else:
                name_mismatch += 1
            
            # 检查组织关联
            if db_user_info['organ_affiliation'] and db_user_info['organ_affiliation'] in db_orgs:
                db_org_info = db_orgs[db_user_info['organ_affiliation']]
                result['db_dept_name'] = db_org_info['organ_name']
                
                # 比较部门名称
                if (csv_info['dept_name'] in db_org_info['organ_name'] or 
                    db_org_info['organ_name'] in csv_info['dept_name'] or
                    csv_info['dept_name'] == db_org_info['organ_name']):
                    result['dept_match'] = True
                    
                # 确定最终状态
                if result['name_match'] and result['dept_match']:
                    result['status'] = 'exact_match'
                    exact_matched += 1
                elif result['name_match']:
                    result['status'] = 'dept_mismatch'
                    account_matched += 1
                else:
                    result['status'] = 'name_and_dept_mismatch'
                    account_matched += 1
            else:
                result['status'] = 'no_org_in_db'
                if result['name_match']:
                    account_matched += 1
                else:
                    account_matched += 1
        else:
            result['status'] = 'account_not_found'
            account_not_found += 1
        
        validation_results.append(result)
    
    # 输出统计结果
    print(f"\n=== 基于工号的验证结果 ===")
    print(f"CSV文件总用户数: {total_csv_users}")
    print(f"工号匹配且部门匹配: {exact_matched} ({exact_matched/total_csv_users*100:.1f}%)")
    print(f"工号匹配但部门不匹配: {account_matched} ({account_matched/total_csv_users*100:.1f}%)")
    print(f"工号未找到: {account_not_found} ({account_not_found/total_csv_users*100:.1f}%)")
    print(f"姓名不匹配: {name_mismatch} ({name_mismatch/total_csv_users*100:.1f}%)")
    
    total_account_matched = exact_matched + account_matched
    print(f"工号匹配总数: {total_account_matched} ({total_account_matched/total_csv_users*100:.1f}%)")
    
    # 保存详细验证结果
    output_file = "基于工号的验证结果.csv"
    with open(output_file, 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['CSV工号', 'CSV用户名', 'CSV部门名', 'CSV部门ID', '验证状态', 
                        '数据库用户名', '数据库部门名', '数据库组织ID', '姓名匹配', '部门匹配'])
        
        for result in validation_results:
            writer.writerow([
                result['csv_account'],
                result['csv_user_name'],
                result['csv_dept_name'],
                result['csv_dept_id'],
                result['status'],
                result['db_user_name'] or '',
                result['db_dept_name'] or '',
                result['db_org_id'] or '',
                result['name_match'],
                result['dept_match']
            ])
    
    print(f"\n详细验证结果已保存到: {output_file}")
    
    # 显示一些不匹配的例子
    print(f"\n=== 部门不匹配示例 (前5个) ===")
    dept_mismatch_count = 0
    for result in validation_results:
        if result['status'] == 'dept_mismatch' and dept_mismatch_count < 5:
            print(f"工号: {result['csv_account']}, 用户: {result['csv_user_name']}")
            print(f"  CSV部门: {result['csv_dept_name']}")
            print(f"  数据库部门: {result['db_dept_name']}")
            print()
            dept_mismatch_count += 1
    
    # 显示工号未找到的例子
    print(f"\n=== 工号未找到示例 (前5个) ===")
    not_found_count = 0
    for result in validation_results:
        if result['status'] == 'account_not_found' and not_found_count < 5:
            print(f"工号: {result['csv_account']}, 用户: {result['csv_user_name']}")
            print(f"  CSV部门: {result['csv_dept_name']}")
            not_found_count += 1
            if not_found_count >= 5:
                break

def main():
    """主函数"""
    print("=== 基于工号的用户部门关联验证工具 ===")
    
    # 文件路径
    user_sql_file = "t_user.sql"
    org_sql_file = "t_org_structure.sql"
    csv_file = "用户部门对应关系.csv"
    
    # 提取数据库数据（按工号索引）
    db_users, db_account_duplicates = extract_database_users_by_account(user_sql_file)
    db_orgs = extract_database_orgs(org_sql_file)
    
    # 加载CSV数据（按工号索引）
    csv_mapping, csv_account_duplicates = load_csv_mapping_by_account(csv_file)
    
    # 基于工号验证关联关系
    validate_by_account(db_users, db_orgs, csv_mapping, db_account_duplicates, csv_account_duplicates)
    
    print("\n=== 验证完成 ===")

if __name__ == "__main__":
    main()
