#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析缺失部门是否可以通过HR_DEPARTMENT.csv中的STR_SN字段匹配
"""

import csv

def main():
    # 读取HR_DEPARTMENT.csv，建立STR_SN到部门信息的映射
    hr_dept_by_sn = {}
    with open('HR_DEPARTMENT.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            sn = row['STR_SN'].strip()
            if sn:
                hr_dept_by_sn[sn] = {
                    'id': row['ID'],
                    'name': row['STR_NAME'],
                    'desc': row['STR_DESC'],
                    'father_id': row['FATHER_ID']
                }

    # 需要创建的部门列表
    missing_depts = [
        '点维一作业区', '电值班', '维修班', '点维作业区', '点维二作业区',
        '第二炼钢厂生产科调度室', '第二烧结厂烧结四车间混配班', '第二烧结厂烧结四车间烧结班',
        '仪表班', '热处理班', '综合管理室', '大棒厂生产作业区乙班', '电工班', '生产技术室',
        '第二烧结厂烧结四车间整粒筛分班', '第一烧结厂环保车间放灰班', '电炉检验班',
        '大棒厂生产作业区丁班', '发货班', '高配主电班', '大棒厂生产作业区丙班',
        '质量管理室', '第一烧结厂烧结一车间烧结班', '台下丁班', '第一烧结厂烧结一车间混配班',
        '台下甲班', '台上丁班', '生产乙班', '驾驶班', '大棒厂精整作业区行车大班',
        '原料中控班', '第一烧结厂环保车间除尘班', '生产丁班', '大棒厂生产作业区甲班',
        '第一烧结厂原料车间原料供料班', '第一烧结厂电修车间电工班', '第一烧结厂烧结一车间整粒筛分班',
        '中棒厂生产作业区甲班', '点检班', '一班'
    ]

    print('通过STR_SN匹配分析：')
    print('=' * 60)

    found_count = 0
    not_found = []
    matches = []

    for dept_name in missing_depts:
        found = False
        for sn, dept_info in hr_dept_by_sn.items():
            # 尝试多种匹配方式
            if (dept_name in dept_info['name'] or 
                dept_info['name'] in dept_name or
                dept_name == dept_info['name']):
                matches.append({
                    'missing_name': dept_name,
                    'hr_sn': sn,
                    'hr_name': dept_info['name'],
                    'hr_id': dept_info['id'],
                    'hr_desc': dept_info['desc']
                })
                found = True
                found_count += 1
                break
        
        if not found:
            not_found.append(dept_name)

    # 输出匹配结果
    print(f'找到匹配的部门 ({found_count}个):')
    for match in matches:
        print(f'✓ {match["missing_name"]} -> STR_SN: {match["hr_sn"]}, HR名称: {match["hr_name"]}')
    
    print(f'\n未找到匹配的部门 ({len(not_found)}个):')
    for dept in not_found:
        print(f'  - {dept}')

    print(f'\n匹配结果统计：')
    print(f'找到匹配: {found_count}个')
    print(f'未找到匹配: {len(not_found)}个')
    print(f'匹配率: {found_count/len(missing_depts)*100:.1f}%')

if __name__ == "__main__":
    main()
