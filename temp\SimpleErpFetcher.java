import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import org.w3c.dom.*;
import java.io.StringReader;
import org.xml.sax.InputSource;

/**
 * 简化版ERP部门数据获取工具
 * 只获取ERP接口数据并转换为HR_DEPARTMENT格式保存到文件
 */
public class SimpleErpFetcher {
    
    // ERP接口配置
    private static final String ERP_URL = "https://dmzesb.nisco.cn/dmzesb/XYTQZSJ/MDM/services/GetDatasFromMDMQuery";
    
    public static void main(String[] args) {
        SimpleErpFetcher fetcher = new SimpleErpFetcher();
        try {
            System.out.println("=== ERP部门数据获取工具（简化版）===");
            
            // 1. 调用ERP接口获取数据
            String erpResponse = fetcher.callErpApi();
            if (erpResponse == null) {
                System.out.println("获取ERP数据失败");
                return;
            }
            
            // 2. 解析ERP响应数据
            List<Map<String, String>> erpDepartments = fetcher.parseErpResponse(erpResponse);
            System.out.println("ERP接口返回部门数量: " + erpDepartments.size());
            
            if (erpDepartments.isEmpty()) {
                System.out.println("未获取到部门数据，程序结束");
                return;
            }
            
            // 3. 转换为HR_DEPARTMENT格式
            List<HrDepartment> hrDepartments = fetcher.convertToHrFormat(erpDepartments);
            System.out.println("转换为HR格式部门数量: " + hrDepartments.size());
            
            // 4. 保存数据到文件
            fetcher.saveErpDataToFile(hrDepartments);
            fetcher.saveRawDataToFile(erpDepartments);
            
            // 5. 显示部分数据样本
            fetcher.showSampleData(hrDepartments);
            
        } catch (Exception e) {
            System.err.println("程序执行出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 调用ERP接口获取部门数据
     */
    private String callErpApi() {
        try {
            // 构建SOAP请求体
            String soapRequest = buildSoapRequest();
            
            System.out.println("正在调用ERP接口...");
            System.out.println("URL: " + ERP_URL);
            
            // 创建HTTP连接
            URL url = new URL(ERP_URL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "text/xml; charset=utf-8");
            connection.setRequestProperty("SOAPAction", "http://tempuri.org/GetOrgInfoFromMDM");
            connection.setRequestProperty("User-Agent", "Java/ERP-Fetcher");
            connection.setDoOutput(true);
            connection.setConnectTimeout(30000); // 30秒连接超时
            connection.setReadTimeout(60000);    // 60秒读取超时
            
            // 发送请求
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = soapRequest.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
            
            // 读取响应
            int responseCode = connection.getResponseCode();
            System.out.println("响应状态码: " + responseCode);
            
            StringBuilder response = new StringBuilder();
            try (BufferedReader br = new BufferedReader(new InputStreamReader(
                    responseCode == 200 ? connection.getInputStream() : connection.getErrorStream(),
                    StandardCharsets.UTF_8))) {
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
            }
            
            String responseText = response.toString();
            
            // 保存原始响应到文件
            try (FileWriter writer = new FileWriter("erp_response.xml", StandardCharsets.UTF_8)) {
                writer.write(responseText);
            }
            System.out.println("原始响应已保存到: erp_response.xml");
            
            return responseText;
            
        } catch (Exception e) {
            System.err.println("调用ERP接口失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 构建SOAP请求体
     */
    private String buildSoapRequest() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        String startDate = "2015/01/01 00:00:00";
        String endDate = sdf.format(new Date());
        
        System.out.println("时间范围: " + startDate + " 到 " + endDate);
        
        return "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
               "<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">\n" +
               "  <soap:Header/>\n" +
               "  <soap:Body>\n" +
               "    <tem:GetOrgInfoFromMDM>\n" +
               "      <tem:InXml><![CDATA[<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
               "        <tem:GetOrgInfoFromMDM>\n" +
               "          <![CDATA[<UDEF1>" + startDate + "</UDEF1>\n" +
               "          <UDEF2>" + endDate + "</UDEF2>]]>\n" +
               "        </tem:GetOrgInfoFromMDM>]]>\n" +
               "      </tem:InXml>\n" +
               "    </tem:GetOrgInfoFromMDM>\n" +
               "  </soap:Body>\n" +
               "</soap:Envelope>";
    }
    
    /**
     * 解析ERP响应数据
     */
    private List<Map<String, String>> parseErpResponse(String responseText) {
        List<Map<String, String>> departments = new ArrayList<>();
        
        try {
            // 首先检查是否是JSON错误响应
            if (responseText.trim().startsWith("{")) {
                System.out.println("收到JSON响应，可能是错误信息:");
                System.out.println(responseText);
                return departments;
            }
            
            System.out.println("开始解析SOAP响应...");
            
            // 解析SOAP响应
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new InputSource(new StringReader(responseText)));
            
            // 查找GetOrgInfoFromMDMResult元素
            NodeList resultNodes = doc.getElementsByTagName("GetOrgInfoFromMDMResult");
            if (resultNodes.getLength() == 0) {
                System.out.println("未找到GetOrgInfoFromMDMResult元素");
                printXmlStructure(doc.getDocumentElement(), 0, 3);
                return departments;
            }
            
            String innerXml = resultNodes.item(0).getTextContent();
            if (innerXml == null || innerXml.trim().isEmpty()) {
                System.out.println("GetOrgInfoFromMDMResult为空");
                return departments;
            }
            
            System.out.println("提取的内部XML长度: " + innerXml.length());
            
            // 解析内部XML
            Document innerDoc = builder.parse(new InputSource(new StringReader(innerXml)));
            
            // 尝试多种可能的元素名称
            String[] possibleTags = {"DATA", "ITEM", "ROW", "RECORD", "Department", "Org"};
            
            for (String tag : possibleTags) {
                NodeList dataNodes = innerDoc.getElementsByTagName(tag);
                if (dataNodes.getLength() > 0) {
                    System.out.println("找到 " + dataNodes.getLength() + " 个 " + tag + " 元素");
                    
                    for (int i = 0; i < dataNodes.getLength(); i++) {
                        Node dataNode = dataNodes.item(i);
                        Map<String, String> dept = new HashMap<>();
                        
                        NodeList children = dataNode.getChildNodes();
                        for (int j = 0; j < children.getLength(); j++) {
                            Node child = children.item(j);
                            if (child.getNodeType() == Node.ELEMENT_NODE) {
                                String key = child.getNodeName();
                                String value = child.getTextContent();
                                dept.put(key, value != null ? value.trim() : "");
                            }
                        }
                        
                        if (!dept.isEmpty()) {
                            departments.add(dept);
                        }
                    }
                    break; // 找到数据就退出循环
                }
            }
            
            if (departments.isEmpty()) {
                System.out.println("未找到部门数据，尝试打印XML结构...");
                printXmlStructure(innerDoc.getDocumentElement(), 0, 5);
            }
            
        } catch (Exception e) {
            System.err.println("解析ERP响应失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return departments;
    }
    
    /**
     * 打印XML结构用于调试
     */
    private void printXmlStructure(Node node, int level, int maxLevel) {
        if (level > maxLevel) return;
        
        String indent = "  ".repeat(level);
        System.out.println(indent + node.getNodeName() + " (" + node.getNodeType() + ")");
        
        NodeList children = node.getChildNodes();
        for (int i = 0; i < Math.min(children.getLength(), 5); i++) {
            Node child = children.item(i);
            if (child.getNodeType() == Node.ELEMENT_NODE) {
                printXmlStructure(child, level + 1, maxLevel);
            }
        }
    }
    
    /**
     * 转换ERP数据为HR_DEPARTMENT格式
     */
    private List<HrDepartment> convertToHrFormat(List<Map<String, String>> erpDepartments) {
        List<HrDepartment> hrDepartments = new ArrayList<>();
        
        System.out.println("开始转换数据格式...");
        
        // 先显示第一个部门的所有字段，帮助了解数据结构
        if (!erpDepartments.isEmpty()) {
            System.out.println("第一个部门的字段:");
            Map<String, String> firstDept = erpDepartments.get(0);
            for (Map.Entry<String, String> entry : firstDept.entrySet()) {
                System.out.println("  " + entry.getKey() + " = " + entry.getValue());
            }
        }
        
        for (Map<String, String> erpDept : erpDepartments) {
            HrDepartment hrDept = new HrDepartment();
            
            // 映射字段 - 根据实际ERP字段调整
            hrDept.setOrgCode(erpDept.get("ORGCODE"));
            hrDept.setOrgName(erpDept.get("ORGNAME"));
            hrDept.setPreOrgCode(erpDept.get("PREORGCODE"));
            hrDept.setOrgLevel(erpDept.get("ORGLEVEL"));
            hrDept.setOrgType(erpDept.get("ORGTYPE"));
            hrDept.setOrgStatus(erpDept.get("ORGSTATUS"));
            hrDept.setCreateTime(erpDept.get("CREATETIME"));
            hrDept.setUpdateTime(erpDept.get("UPDATETIME"));
            
            // 如果主要字段为空，尝试其他可能的字段名
            if (hrDept.getOrgCode() == null || hrDept.getOrgCode().isEmpty()) {
                hrDept.setOrgCode(erpDept.get("ORG_CODE"));
            }
            if (hrDept.getOrgName() == null || hrDept.getOrgName().isEmpty()) {
                hrDept.setOrgName(erpDept.get("ORG_NAME"));
            }
            
            hrDepartments.add(hrDept);
        }
        
        return hrDepartments;
    }
    
    /**
     * 保存ERP数据到CSV文件
     */
    private void saveErpDataToFile(List<HrDepartment> departments) {
        try (FileWriter writer = new FileWriter("erp_departments_hr_format.csv", StandardCharsets.UTF_8)) {
            // 写入CSV头
            writer.write("org_code,org_name,pre_org_code,org_level,org_type,org_status,create_time,update_time\n");
            
            // 写入数据
            for (HrDepartment dept : departments) {
                writer.write(String.format("%s,%s,%s,%s,%s,%s,%s,%s\n",
                    csvEscape(dept.getOrgCode()),
                    csvEscape(dept.getOrgName()),
                    csvEscape(dept.getPreOrgCode()),
                    csvEscape(dept.getOrgLevel()),
                    csvEscape(dept.getOrgType()),
                    csvEscape(dept.getOrgStatus()),
                    csvEscape(dept.getCreateTime()),
                    csvEscape(dept.getUpdateTime())
                ));
            }
            
            System.out.println("HR格式部门数据已保存到: erp_departments_hr_format.csv");
            
        } catch (IOException e) {
            System.err.println("保存HR格式文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 保存原始ERP数据到CSV文件
     */
    private void saveRawDataToFile(List<Map<String, String>> departments) {
        if (departments.isEmpty()) return;
        
        try (FileWriter writer = new FileWriter("erp_departments_raw.csv", StandardCharsets.UTF_8)) {
            // 获取所有字段名
            Set<String> allFields = new HashSet<>();
            for (Map<String, String> dept : departments) {
                allFields.addAll(dept.keySet());
            }
            
            List<String> fieldList = new ArrayList<>(allFields);
            Collections.sort(fieldList);
            
            // 写入CSV头
            writer.write(String.join(",", fieldList) + "\n");
            
            // 写入数据
            for (Map<String, String> dept : departments) {
                List<String> values = new ArrayList<>();
                for (String field : fieldList) {
                    values.add(csvEscape(dept.get(field)));
                }
                writer.write(String.join(",", values) + "\n");
            }
            
            System.out.println("原始ERP数据已保存到: erp_departments_raw.csv");
            
        } catch (IOException e) {
            System.err.println("保存原始数据文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 显示数据样本
     */
    private void showSampleData(List<HrDepartment> departments) {
        System.out.println("\n=== 数据样本 (前5个部门) ===");
        
        for (int i = 0; i < Math.min(5, departments.size()); i++) {
            HrDepartment dept = departments.get(i);
            System.out.println("部门 " + (i + 1) + ":");
            System.out.println("  机构代码: " + dept.getOrgCode());
            System.out.println("  机构名称: " + dept.getOrgName());
            System.out.println("  上级代码: " + dept.getPreOrgCode());
            System.out.println("  机构级别: " + dept.getOrgLevel());
            System.out.println("  机构类型: " + dept.getOrgType());
            System.out.println("  机构状态: " + dept.getOrgStatus());
            System.out.println();
        }
    }
    
    private String csvEscape(String value) {
        if (value == null) return "";
        if (value.contains(",") || value.contains("\"") || value.contains("\n")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }
        return value;
    }
    
    /**
     * HR部门数据模型
     */
    static class HrDepartment {
        private String orgCode;
        private String orgName;
        private String preOrgCode;
        private String orgLevel;
        private String orgType;
        private String orgStatus;
        private String createTime;
        private String updateTime;
        
        // Getters and Setters
        public String getOrgCode() { return orgCode; }
        public void setOrgCode(String orgCode) { this.orgCode = orgCode; }
        
        public String getOrgName() { return orgName; }
        public void setOrgName(String orgName) { this.orgName = orgName; }
        
        public String getPreOrgCode() { return preOrgCode; }
        public void setPreOrgCode(String preOrgCode) { this.preOrgCode = preOrgCode; }
        
        public String getOrgLevel() { return orgLevel; }
        public void setOrgLevel(String orgLevel) { this.orgLevel = orgLevel; }
        
        public String getOrgType() { return orgType; }
        public void setOrgType(String orgType) { this.orgType = orgType; }
        
        public String getOrgStatus() { return orgStatus; }
        public void setOrgStatus(String orgStatus) { this.orgStatus = orgStatus; }
        
        public String getCreateTime() { return createTime; }
        public void setCreateTime(String createTime) { this.createTime = createTime; }
        
        public String getUpdateTime() { return updateTime; }
        public void setUpdateTime(String updateTime) { this.updateTime = updateTime; }
    }
}
