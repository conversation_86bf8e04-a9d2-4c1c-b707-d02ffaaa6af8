package com.dfit.percode.util;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.BufferedWriter;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicLong;

public class ExcelToSqlConverter {

    private static final long START_ID = 1000000000000000000L;
    private static final AtomicLong idGenerator = new AtomicLong(START_ID);
    private static final Map<String, Long> idMapping = new HashMap<>();

    public static void main(String[] args) {
        String excelFilePath = "HR_DEPARTMENT.xlsx";
        String outputSqlFilePath = "docs/import_hr_department_numeric.sql";
        String idMappingFilePath = "docs/id_mapping.properties";

        try {
            convertExcelToSql(excelFilePath, outputSqlFilePath, idMappingFilePath);
            System.out.println("Successfully converted Excel to SQL script with numeric IDs.");
            System.out.println("Output SQL file: " + outputSqlFilePath);
            System.out.println("Output ID Mapping file: " + idMappingFilePath);
        } catch (IOException e) {
            System.err.println("Error during conversion: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void convertExcelToSql(String excelPath, String sqlPath, String mappingPath) throws IOException {
        try (FileInputStream fis = new FileInputStream(excelPath);
             Workbook workbook = new XSSFWorkbook(fis);
             BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(sqlPath), StandardCharsets.UTF_8))) {

            Sheet sheet = workbook.getSheet("HR_DEPARTMENT");
            if (sheet == null) {
                throw new IOException("Sheet 'HR_DEPARTMENT' not found in the Excel file.");
            }

            Iterator<Row> rowIterator = sheet.iterator();

            // Skip header row
            if (rowIterator.hasNext()) {
                rowIterator.next();
            }
            
            writer.write("-- SQL Script to Import HR Department Structure with Numeric IDs\n\n");
            writer.write("-- Step 1: It's recommended to backup your t_org_structure table first.\n");
            writer.write("-- Step 2: Clean up existing synchronized data (data_source = 2).\n");
            writer.write("-- UPDATE \"public\".\"t_org_structure\" SET \"is_del\" = true WHERE \"data_source\" = 2;\n");
            writer.write("-- DELETE FROM \"public\".\"t_org_structure\" WHERE \"data_source\" = 2 AND \"is_del\" = true;\n\n");
            writer.write("-- Step 3: Insert the new organization structure from HR_DEPARTMENT.xlsx\n");

            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                
                Cell idCell = row.getCell(0);
                Cell nameCell = row.getCell(2);
                Cell parentIdCell = row.getCell(4);

                if (idCell == null || nameCell == null || parentIdCell == null) {
                    continue; // Skip incomplete rows
                }

                String oldId = getCellStringValue(idCell);
                String organName = getCellStringValue(nameCell).replace("'", "''"); // Escape single quotes
                String oldParentId = getCellStringValue(parentIdCell);

                if (oldId.isEmpty() || organName.isEmpty()) {
                    continue; // Skip rows with empty ID or name
                }

                long newId = idMapping.computeIfAbsent(oldId, k -> idGenerator.getAndIncrement());
                
                String newParentIdValue;
                if (oldParentId.isEmpty() || "0".equals(oldParentId) || "1".equals(oldParentId) || oldId.equals(oldParentId)) {
                    newParentIdValue = "0";
                } else {
                    long newParentId = idMapping.computeIfAbsent(oldParentId, k -> idGenerator.getAndIncrement());
                    newParentIdValue = String.valueOf(newParentId);
                }

                String sql = String.format(
                    "INSERT INTO \"public\".\"t_org_structure\" (id, organ_name, pre_id, is_del, data_source, create_time, modify_time) VALUES (%d, '%s', %s, 'f', 2, NOW(), NOW());\n",
                    newId, organName, newParentIdValue
                );
                writer.write(sql);
            }

            // Save the ID mapping to a properties file
            Properties properties = new Properties();
            for (Map.Entry<String, Long> entry : idMapping.entrySet()) {
                properties.setProperty(entry.getKey(), String.valueOf(entry.getValue()));
            }
            try (FileOutputStream fos = new FileOutputStream(mappingPath)) {
                properties.store(fos, "HR Department ID Mapping: old_id=new_id");
            }
        }
    }

    private static String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        cell.setCellType(CellType.STRING);
        return cell.getStringCellValue().trim();
    }
}