#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析部门不匹配的情况
"""

import csv

def analyze_mismatches():
    """分析不匹配的情况"""
    
    dept_mismatch = []
    no_org = []
    name_mismatch = []
    
    with open('基于工号的验证结果.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for row in reader:
            status = row['验证状态']
            if status == 'dept_mismatch':
                dept_mismatch.append(row)
            elif status == 'no_org_in_db':
                no_org.append(row)
            elif status in ['name_and_dept_mismatch', 'name_mismatch']:
                name_mismatch.append(row)
    
    print(f'部门不匹配数量: {len(dept_mismatch)}')
    print(f'数据库无部门数量: {len(no_org)}')
    print(f'姓名不匹配数量: {len(name_mismatch)}')
    
    print('\n=== 部门不匹配示例 (前10个) ===')
    for i, row in enumerate(dept_mismatch[:10]):
        print(f'{i+1}. 工号: {row["CSV工号"]}, 姓名: {row["CSV用户名"]}')
        print(f'   CSV部门: {row["CSV部门名"]}')
        print(f'   数据库部门: {row["数据库部门名"]}')
        print()
    
    print('\n=== 数据库无部门示例 (前10个) ===')
    for i, row in enumerate(no_org[:10]):
        print(f'{i+1}. 工号: {row["CSV工号"]}, 姓名: {row["CSV用户名"]}')
        print(f'   CSV部门: {row["CSV部门名"]}')
        print()
    
    print('\n=== 姓名不匹配示例 ===')
    for i, row in enumerate(name_mismatch):
        print(f'{i+1}. 工号: {row["CSV工号"]}')
        print(f'   CSV姓名: {row["CSV用户名"]}')
        print(f'   数据库姓名: {row["数据库用户名"]}')
        print(f'   CSV部门: {row["CSV部门名"]}')
        print(f'   数据库部门: {row["数据库部门名"]}')
        print()
    
    # 分析部门不匹配的类型
    print('\n=== 部门不匹配类型分析 ===')
    mismatch_types = {}
    for row in dept_mismatch:
        csv_dept = row["CSV部门名"]
        db_dept = row["数据库部门名"]
        
        # 简单分类
        if "未知部门" in csv_dept:
            mismatch_type = "CSV中为未知部门"
        elif not db_dept or db_dept.strip() == "":
            mismatch_type = "数据库部门为空"
        elif len(csv_dept) > len(db_dept):
            mismatch_type = "CSV部门名更详细"
        elif len(db_dept) > len(csv_dept):
            mismatch_type = "数据库部门名更详细"
        else:
            mismatch_type = "完全不同的部门"
        
        if mismatch_type not in mismatch_types:
            mismatch_types[mismatch_type] = []
        mismatch_types[mismatch_type].append(row)
    
    for mismatch_type, rows in mismatch_types.items():
        print(f'{mismatch_type}: {len(rows)}个')
        if len(rows) <= 3:
            for row in rows:
                print(f'  - {row["CSV用户名"]} ({row["CSV工号"]})')
                print(f'    CSV: {row["CSV部门名"]}')
                print(f'    DB:  {row["数据库部门名"]}')
        else:
            for row in rows[:3]:
                print(f'  - {row["CSV用户名"]} ({row["CSV工号"]})')
                print(f'    CSV: {row["CSV部门名"]}')
                print(f'    DB:  {row["数据库部门名"]}')
            print(f'  ... 还有{len(rows)-3}个')
        print()

if __name__ == "__main__":
    analyze_mismatches()
