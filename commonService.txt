public void updateAllDepartmentFromErp() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
        String edate = LocalDateTime.now().format(formatter);
        String sdate = "2020/01/01 00:00:00";
        for (String ndate = sdate; ndate.compareTo(edate) < 0; ) {
            if (LocalDateTime.parse(ndate, formatter).plusDays(1).format(formatter).compareTo(edate) > 0) {
                updateDepartmentFromErp(ndate, edate);
            } else {
                updateDepartmentFromErp(ndate, LocalDateTime.parse(ndate, formatter).plusDays(1).format(formatter));
            }
            ndate = LocalDateTime.parse(ndate, formatter).plusDays(1).format(formatter);
        }
        List<String> mids = new ArrayList<>();
        mids.add("X");
        String mgs = "X";
        List<HrDepartment> departments1 = commonMapper.getHrDepartmentByStrsn("X");
        for (HrDepartment department : departments1) {
            commonMapper.removeHrDepartmentById(department.getId());
            mids.add(department.getId());
            mgs = department.getId();
        }
        List<HrDepartment> departments2 = commonMapper.getHrDepartmentByFatheridBug();
        for (HrDepartment hrDepartment : departments2) {
            if (mids.contains(hrDepartment.getFatherId())) {
                hrDepartment.setFatherId("1");
            } else {
                List<HrDepartment> departments3 = commonMapper.getHrDepartmentByStrsn(hrDepartment.getFatherId());
                if (departments3.size() > 0) {
                    hrDepartment.setFatherId(departments3.get(0).getId());
                } else {
                }
            }
            this.updateDepartment(hrDepartment);
        }
        List<HrDepartment> departments3 = commonMapper.getHrDepartmentByFatherid(mgs);
        for (HrDepartment hrDepartment : departments3) {
            hrDepartment.setFatherId("1");
            this.updateDepartment(hrDepartment);
        }
    }
	
public void updateDepartment(HrDepartment department) throws ServiceException {
        List<HrDepartment> hrDepartmentByStrSn = commonMapper.getHrDepartmentByStrsn(department.getStrSn());
        if (hrDepartmentByStrSn.size() > 0) {
            for (HrDepartment o : hrDepartmentByStrSn) {
                if (!o.getId().equals(department.getId())) {
                    ServiceAssert.isTrue(false, "部门编码已存在，部门修改失败！");
                }
            }
        }
        ServiceAssert.isNotNull(department.getId(), "id不能为空");
        department.setUpdateUserNo(AuthUtil.getLoginUserNo());
        department.setUpdateDateTime(DateUtil.getCurrentTime());
        commonMapper.updateHrDepartment(department);
        redisService.remove(CACHE_NAME_SPACE + "getDeptUserTree", CACHE_NAME_SPACE + "getDeptUserTreeLess", CACHE_NAME_SPACE + "getDeptUserTreeList", CACHE_NAME_SPACE + "getDeptTree", CACHE_NAME_SPACE + "getLeftTree");
    }
	
public void updateDepartmentFromErp(String sdate, String edate) {
        String xmltext = getXmlInfoDepartment(sdate, edate);
        String urltext = "http://esb.nisco.cn:6001/esb/QLM/MDM/services/GetDatasFromMDMQuery";
        String text = creatPost(urltext, xmltext);
        int beginIndex = 0;
        int endIndex = 0;
        if (text.indexOf("<GetOrgInfoFromMDMResult>") >= 0) {
            beginIndex = text.indexOf("<GetOrgInfoFromMDMResult>") + 25;
            endIndex = text.lastIndexOf("</GetOrgInfoFromMDMResult>");
        }
        String loginName = text.toString().substring(beginIndex, endIndex);
        List<Map<String, String>> list = getXmlDepartment(loginName);
        list.forEach((e) -> {
            String no = e.get("ORGCODE");
            List<HrDepartment> departments = commonMapper.getHrDepartmentByStrsn(e.get("ORGCODE"));
            if (departments.size() > 0) {
                if (e.get("ISHISTORY").equals("1")) {
                    // 删除
                    departments.forEach((f) -> {
                        this.removeDepartment(f.getId());
                    });
//                  commonMapper.delHrDepartmentByStrsn(e.get("ORGCODE"));
                } else if (e.get("ISHISTORY").equals("0")) {
                    // 修改
                    departments.forEach((f) -> {
                        HrDepartment hrDepartment = f;
                        hrDepartment.setStrSn(e.get("ORGCODE"));
                        hrDepartment.setStrName(e.get("ORGNAME"));
                        hrDepartment.setStrDesc(e.get("ORAALLNAME"));
                        List<HrDepartment> departments1 = commonMapper.getHrDepartmentByStrsn(e.get("PNODECODE"));
                        if (departments1.size() > 0) {
                            hrDepartment.setFatherId(departments1.get(0).getId());
                        } else {
                            hrDepartment.setFatherId(e.get("PNODECODE"));
                        }
                        hrDepartment.setSort(0);
                        this.updateDepartment(hrDepartment);
                    });
//                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//                    String datetime=LocalDateTime.now().format(formatter);
//                    Map<String, String> map=e;
//                    map.put("UPDATE_USER_NO","admin");
//                    map.put("UPDATE_DATE_TIME",datetime);
//                    map.put("COMPANY_ID","1");
//                    commonMapper.updHrDepartmentByStrsn(map.get("ORGNAME"),map.get("ORAALLNAME"),map.get("PNODECODE"),map.get("USERPREDEF_11"),map.get("UPDATE_USER_NO"),map.get("UPDATE_DATE_TIME"),map.get("COMPANY_ID"),map.get("ORGCODE"));
                }
            } else {
                if (e.get("ISHISTORY").equals("0")) {
                    // 新增
                    HrDepartment hrDepartment = new HrDepartment();
                    hrDepartment.setStrSn(e.get("ORGCODE"));
                    hrDepartment.setStrName(e.get("ORGNAME"));
                    hrDepartment.setStrDesc(e.get("ORAALLNAME"));
                    List<HrDepartment> departments1 = commonMapper.getHrDepartmentByStrsn(e.get("PNODECODE"));
                    if (departments1.size() > 0) {
                        hrDepartment.setFatherId(departments1.get(0).getId());
                    } else {
                        hrDepartment.setFatherId(e.get("PNODECODE"));
                    }
                    hrDepartment.setSort(0);
                    this.saveDepartment(hrDepartment);
//                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//                String datetime=LocalDateTime.now().format(formatter);
//                Map<String, String> map=e;
//                map.put("ID",UUIDUtil.getUUID());
//                map.put("CREATE_USER_NO","admin");
//                map.put("CREATE_DATE_TIME",datetime);
//                map.put("UPDATE_USER_NO","admin");
//                map.put("UPDATE_DATE_TIME",datetime);
//                map.put("COMPANY_ID","1");
//                int a = commonMapper.insHrDepartment(map.get("ID"),map.get("ORGCODE"),map.get("ORGNAME"),map.get("ORAALLNAME"),map.get("PNODECODE"),null,map.get("CREATE_USER_NO"),map.get("CREATE_DATE_TIME"),map.get("UPDATE_USER_NO"),map.get("UPDATE_DATE_TIME"),map.get("COMPANY_ID"),map.get("USERPREDEF_11"));
                }
            }
        });
//        list.forEach((e) -> {
//            String no=e.get("ORGCODE");
//            List<HrDepartment> departments=commonMapper.getHrDepartmentByStrsn(e.get("ORGCODE"));
//            if(departments.size()>0){
//                if(e.get("ISHISTORY").equals("1")){
//                    //删除
//                    commonMapper.delHrDepartmentByStrsn(e.get("ORGCODE"));
//                } else if (e.get("ISHISTORY").equals("0")) {
//                    //修改
//                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//                    String datetime=LocalDateTime.now().format(formatter);
//                    Map<String, String> map=e;
//                    map.put("UPDATE_USER_NO","admin");
//                    map.put("UPDATE_DATE_TIME",datetime);
//                    map.put("COMPANY_ID","1");
//                    commonMapper.updHrDepartmentByStrsn(map.get("ORGNAME"),map.get("ORAALLNAME"),map.get("PNODECODE"),map.get("USERPREDEF_11"),map.get("UPDATE_USER_NO"),map.get("UPDATE_DATE_TIME"),map.get("COMPANY_ID"),map.get("ORGCODE"));
//                }
//            }else{
//                //新增
//                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//                String datetime=LocalDateTime.now().format(formatter);
//                Map<String, String> map=e;
//                map.put("ID",UUIDUtil.getUUID());
//                map.put("CREATE_USER_NO","admin");
//                map.put("CREATE_DATE_TIME",datetime);
//                map.put("UPDATE_USER_NO","admin");
//                map.put("UPDATE_DATE_TIME",datetime);
//                map.put("COMPANY_ID","1");
//
//                int a = commonMapper.insHrDepartment(map.get("ID"),map.get("ORGCODE"),map.get("ORGNAME"),map.get("ORAALLNAME"),map.get("PNODECODE"),null,map.get("CREATE_USER_NO"),map.get("CREATE_DATE_TIME"),map.get("UPDATE_USER_NO"),map.get("UPDATE_DATE_TIME"),map.get("COMPANY_ID"),map.get("USERPREDEF_11"));
//            }
//        });
    }