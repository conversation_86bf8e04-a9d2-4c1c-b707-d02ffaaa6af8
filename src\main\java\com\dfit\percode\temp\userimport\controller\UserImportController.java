package com.dfit.percode.temp.userimport.controller;

import com.dfit.percode.temp.userimport.service.IUserImportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Api(tags = "Temporary User Import API")
@RestController
@RequestMapping("/api/temp/users")
public class UserImportController {

    @Autowired
    private IUserImportService userImportService;

    @ApiOperation("Import users from an Excel file")
    @PostMapping("/upload")
    public ResponseEntity<String> uploadUsers(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return ResponseEntity.badRequest().body("Please upload a file.");
        }

        String result = userImportService.importUsers(file);
        return ResponseEntity.ok(result);
    }
}