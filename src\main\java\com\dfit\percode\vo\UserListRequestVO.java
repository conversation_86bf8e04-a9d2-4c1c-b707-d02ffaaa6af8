package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户列表分页查询请求VO类
 * 按照前端格式要求设计（驼峰命名）
 * 支持分页和多条件搜索
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "UserListRequestVO", description = "用户列表分页查询请求参数")
public class UserListRequestVO {

    @ApiModelProperty(value = "当前页码（从1开始，默认为1）", required = false, example = "1")
    private Integer currentPage;

    @ApiModelProperty(value = "每页大小（默认为10，最大100）", required = false, example = "10")
    private Integer pageSize;

    @ApiModelProperty(value = "用户名称（模糊搜索）", example = "张三")
    private String userName;

    @ApiModelProperty(value = "用户账号（模糊搜索）", example = "admin")
    private String account;

    @ApiModelProperty(value = "用户状态：false-正常，true-停用", example = "false")
    private Boolean userState;

    @ApiModelProperty(value = "创建时间开始", example = "2025-01-01")
    private String startTime;

    @ApiModelProperty(value = "创建时间结束", example = "2025-01-31")
    private String endTime;
}
