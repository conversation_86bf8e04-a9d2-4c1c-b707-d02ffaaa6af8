package com.dfit.percode.temp.userimport.service.impl;

import com.dfit.percode.entity.TUser;
import com.dfit.percode.mapper.TUserMapper;
import com.dfit.percode.temp.userimport.dto.UserImportDto;
import com.dfit.percode.temp.userimport.service.IUserImportService;
import com.dfit.percode.util.UserImportUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileInputStream;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.stream.Collectors;

@Service
public class UserImportServiceImpl implements IUserImportService {

    private static final Logger log = LoggerFactory.getLogger(UserImportServiceImpl.class);

    @Autowired
    private TUserMapper tUserMapper;

    @Override
    @Transactional
    public String importUsers(MultipartFile file) {
        log.info("开始处理用户导入...");
        try {
            InputStream inputStream = file.getInputStream();
            List<UserImportDto> userImportDtoList = UserImportUtil.excelToUserImportDto(inputStream);
            if (userImportDtoList == null || userImportDtoList.isEmpty()) {
                log.warn("Excel文件为空或格式不正确");
                return "导入失败，Excel文件为空或格式不正确";
            }
            log.info("从Excel中解析出 {} 个用户", userImportDtoList.size());

            Properties idMapping = new Properties();
            try (FileInputStream fis = new FileInputStream("docs/id_mapping.properties")) {
                idMapping.load(fis);
            }
            log.info("加载了 {} 个部门ID映射", idMapping.size());

            int successCount = 0;
            long userIdCounter = 2000000000000000000L; // 设置一个与部门ID不冲突的起始值
            for (UserImportDto userImportDto : userImportDtoList) {
                TUser tUser = new TUser();
                tUser.setId(userIdCounter++);
                tUser.setUserName(userImportDto.getName());
                tUser.setAccount(userImportDto.getUserNo());

                String oldDeptId = userImportDto.getDeptId();
                if (idMapping.containsKey(oldDeptId)) {
                    long newDeptId = Long.parseLong(idMapping.getProperty(oldDeptId));
                    tUser.setOrganAffiliation(newDeptId);
                } else {
                    log.warn("用户 '{}' 的部门ID '{}' 在映射文件中不存在，跳过此用户的部门设置", userImportDto.getName(), oldDeptId);
                }

                // 由于我们正在生成新的ID，所以这里总是插入
                tUserMapper.insert(tUser);
                successCount++;
            }
            log.info("成功导入/更新了 {} 个用户", successCount);
            return "导入成功";
        } catch (Exception e) {
            log.error("用户导入失败", e);
            return "导入失败：" + e.getMessage();
        }
    }
}