import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

public class ReadHRDepartmentExcel {
    
    public static void main(String[] args) {
        System.out.println("=== 读取HR_DEPARTMENT.xlsx文件 ===");
        
        try {
            FileInputStream fis = new FileInputStream("HR_DEPARTMENT.xlsx");
            Workbook workbook = new XSSFWorkbook(fis);
            
            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            System.out.println("工作表名称: " + sheet.getSheetName());
            System.out.println("总行数: " + (sheet.getLastRowNum() + 1));
            
            // 读取表头
            Row headerRow = sheet.getRow(0);
            if (headerRow != null) {
                System.out.println("\n=== 表头信息 ===");
                List<String> headers = new ArrayList<>();
                for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                    Cell cell = headerRow.getCell(i);
                    String headerValue = getCellValueAsString(cell);
                    headers.add(headerValue);
                    System.out.println("列" + (i + 1) + ": " + headerValue);
                }
                
                System.out.println("\n=== 前10行数据 ===");
                // 读取前10行数据
                for (int rowIndex = 1; rowIndex <= Math.min(10, sheet.getLastRowNum()); rowIndex++) {
                    Row row = sheet.getRow(rowIndex);
                    if (row != null) {
                        System.out.println("\n第" + (rowIndex + 1) + "行:");
                        for (int colIndex = 0; colIndex < headers.size(); colIndex++) {
                            Cell cell = row.getCell(colIndex);
                            String value = getCellValueAsString(cell);
                            System.out.println("  " + headers.get(colIndex) + ": " + value);
                        }
                    }
                }
                
                // 统计数据
                System.out.println("\n=== 数据统计 ===");
                Map<String, Integer> stats = new HashMap<>();
                
                for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                    Row row = sheet.getRow(rowIndex);
                    if (row != null) {
                        // 统计每列的非空值数量
                        for (int colIndex = 0; colIndex < headers.size(); colIndex++) {
                            Cell cell = row.getCell(colIndex);
                            String value = getCellValueAsString(cell);
                            if (value != null && !value.trim().isEmpty()) {
                                String header = headers.get(colIndex);
                                stats.put(header, stats.getOrDefault(header, 0) + 1);
                            }
                        }
                    }
                }
                
                System.out.println("各列非空值统计:");
                for (String header : headers) {
                    int count = stats.getOrDefault(header, 0);
                    System.out.println("  " + header + ": " + count + " 条");
                }
                
                // 查找可能的部门相关列
                System.out.println("\n=== 部门相关列分析 ===");
                for (String header : headers) {
                    if (header.toLowerCase().contains("dept") || 
                        header.toLowerCase().contains("department") ||
                        header.toLowerCase().contains("部门") ||
                        header.toLowerCase().contains("org") ||
                        header.toLowerCase().contains("组织")) {
                        System.out.println("发现部门相关列: " + header);
                        
                        // 显示该列的前几个值
                        Set<String> uniqueValues = new HashSet<>();
                        int colIndex = headers.indexOf(header);
                        for (int rowIndex = 1; rowIndex <= Math.min(20, sheet.getLastRowNum()); rowIndex++) {
                            Row row = sheet.getRow(rowIndex);
                            if (row != null) {
                                Cell cell = row.getCell(colIndex);
                                String value = getCellValueAsString(cell);
                                if (value != null && !value.trim().isEmpty()) {
                                    uniqueValues.add(value);
                                }
                            }
                        }
                        
                        System.out.println("  前20行中的唯一值 (" + uniqueValues.size() + "个):");
                        int count = 0;
                        for (String value : uniqueValues) {
                            if (count < 10) {
                                System.out.println("    - " + value);
                                count++;
                            } else {
                                System.out.println("    ... 还有" + (uniqueValues.size() - 10) + "个值");
                                break;
                            }
                        }
                    }
                }
            }
            
            workbook.close();
            fis.close();
            
        } catch (IOException e) {
            System.err.println("读取Excel文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 处理数字，避免科学计数法
                    double numValue = cell.getNumericCellValue();
                    if (numValue == Math.floor(numValue)) {
                        return String.valueOf((long) numValue);
                    } else {
                        return String.valueOf(numValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            case BLANK:
                return "";
            default:
                return "";
        }
    }
}
