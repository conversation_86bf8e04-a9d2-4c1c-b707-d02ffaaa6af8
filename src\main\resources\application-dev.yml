server:
  port: 8285
  #配置字符集
  servlet:
    encoding:
      charset: UTF-8
      force: true
      enabled: true



spring:
  datasource:
    url: ****************************************
    username: postgres2
    password: 123456

  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
#  redis:
#    host: localhost
#    port: 6379
#    password:
#    timeout: 500
#    jedis:
#      pool:
#        max-active: 10
#        max-idle: 5
#        min-idle: 1
#        max-wait: 2000


  #  security:
#    form-login:
#      login-page: /login
#      failure-url: /login/login
#  elasticsearch:
#    rest:
#      uris: http://**********:9200
#    username: elastic
#    password: ODa6LEJaFHESXawqWBR

#steel:
#  jwt:
#    location: classpath:steel.jks
#    alias: steel
#    password: steel123
#    tokenTTL: 300m



#minio:
#  endpoint: http://************:9000
#  access-key: root
#  secret-key: 12345678
#  bucket-name: nangangkmap
#  userspace-bucket-name: userspace
#  literature-bucket-name: documents
#  instruction-bucket-name: instructiondocuments
#  draft-bucket-name: draftdocuments


logging:
  #  config: classpath:logback-spring.xml
  level:
    dao: debug
    org:
      mybatis: info

mybatis:
  #mapper配置文件
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.dfit.percode.domain
  #开启驼峰命名
  configuration:
    map-underscore-to-camel-case: true
  type-handlers-package: com.dfit.percode.config


smart-desktop:
  token-prefix: SmartDesktop-
  auth-url: http://172.18.11.139:8081/resource/findAllResourceNoPage

# Sa-Token配置
sa-token:
  # Token名称 (同时也是cookie名称)
  token-name: Authorization
  # Token前缀 (用于Bearer Token格式)
  token-prefix: Bearer
  # Token有效期，单位s，8小时 = 8 * 60 * 60
  timeout: 28800
  # Token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒，4小时 = 4 * 60 * 60
  active-timeout: 14400
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: false
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # JWT秘钥 (用于JWT模式的Token生成和验证)
  jwt-secret-key: abcdefghijklmnopqrstuvwxyz1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ
#  jwt-secret-key: iet_admin
  # 是否输出操作日志
  is-log: true

# 自定义JWT配置（与Sa-Token并存）
custom-jwt:
  # JWT签名密钥（复用Sa-Token的密钥确保一致性）
  secret-key: abcdefghijklmnopqrstuvwxyz1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ
#  secret-key: iet_admin
  # Token过期时间（秒），4小时，与Sa-Token的active-timeout保持一致
  expiration: 14400
  # JWT签发者标识
  issuer: permission-system
  # Token名称
  token-name: Authorization
  # Token前缀
  token-prefix: Bearer

# 超级管理员配置
# 用于配置不受权限管控的超级管理员账号
super-admin:
  # 是否启用超级管理员功能
  enabled: true
  # 超级管理员用户ID列表（与数据库中的用户ID对应）
  user-ids:
    - 1                        # 系统默认管理员
    - 1938155631131365376      # 现有管理员用户ID
  # 超级管理员登录账号列表
  login-accounts:
    - admin                    # 管理员账号
    - superuser               # 超级用户账号
  # 超级管理员用户名列表
  usernames:
    - 管理员                   # 中文用户名
    - 超级管理员               # 超级管理员用户名


