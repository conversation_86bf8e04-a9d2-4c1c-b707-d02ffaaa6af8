//package com.dfit.percode.temp;
//
//import com.dfit.percode.mapper.TOrgStructureMapper;
//import lombok.Data;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.util.StringUtils;
//
//import java.io.IOException;
//import java.nio.charset.StandardCharsets;
//import java.nio.file.Files;
//import java.nio.file.Paths;
//import java.time.LocalDateTime;
//import java.util.*;
//import java.util.concurrent.atomic.AtomicLong;
//import java.util.regex.Matcher;
//import java.util.regex.Pattern;
//import java.util.stream.Collectors;
//
///**
// * 组织架构数据同步工具
// * 临时工具，用于将department_sync_test.sql数据转换并导入到t_org_structure表
// */
//@Component
//@Slf4j
//public class OrganizationSyncTool {
//
//    @Autowired
//    private TOrgStructureMapper orgStructureMapper;
//
//    // 一级部门列表
//    private static final Set<String> LEVEL_1_DEPARTMENTS = Set.of(
//        "新材料科研究院（合署）", "公司办公室", "人力资源部", "企业文化部", "财务部",
//        "党委办公室", "组织部", "党委工作部", "审计部", "集团领导", "风险合规部",
//        "安全环保部", "纪委功公室（党风廉政办公室）", "集团战略发展部",
//        "江苏金珂水务有限公司", "工会", "印尼焦化项目部", "特钢事业部",
//        "数字应用研究院（人工智能研完院）", "南京三金房地产开发有限公司",
//        "南钢退休职工服务中心", "集国资产处置办公室", "江苏金贸钢宝电子商务有限公司2",
//        "团委", "公司领导", "离京鑫智链科技信息有限公司2", "科技质量部",
//        "数字应用研究院", "蔚蓝高科技集团", "战略运营部（产业发展研究院）",
//        "物流中心", "能源动力事业部", "炼铁事业部", "保卫部", "新产业投资集团",
//        "采购中心", "制造部", "板材事业部", "市场部", "江苏金凯节能环保投资控股有限公司",
//        "印尼钢铁项目指挥部", "江苏南钢鑫洋供应链有限公司", "集团宿迁金鑫公司",
//        "南京金智工程技术有限公司", "集团综合资产部", "证券部", "香港金腾公司",
//        "南京钢铁集团国际经济贸易有限公司", "集团工会", "集团财务审计部",
//        "集团宿迁金鑫靖江项目指挥部", "江苏金恒信息科技股份有限公司"
//    );
//
//    // 层级关键词
//    private static final List<String> HIERARCHY_KEYWORDS = Arrays.asList(
//        "事业部", "集团", "公司", "中心", "厂", "车间", "部", "科", "室", "院", "会", "委", "班", "组", "队", "站"
//    );
//
//    /**
//     * 执行数据同步
//     */
//    @Transactional
//    public SyncResult executeSync() {
//        return executeSync("organization-sync-temp/department_sync_test.sql");
//    }
//
//    /**
//     * 执行数据同步
//     */
//    @Transactional
//    public SyncResult executeSync(String filePath) {
//        log.info("开始组织架构数据同步，文件路径: {}", filePath);
//
//        SyncResult result = new SyncResult();
//        result.setStartTime(LocalDateTime.now());
//
//        try {
//            // 1. 解析SQL文件
//            log.info("=== 阶段1: 解析SQL文件 ===");
//            List<DepartmentData> rawData = parseSqlFile(filePath);
//            result.setRawDataCount(rawData.size());
//            log.info("解析完成，原始数据: {} 条", rawData.size());
//
//            // 2. 数据清洗
//            log.info("=== 阶段2: 数据清洗 ===");
//            List<DepartmentData> cleanData = cleanData(rawData);
//            result.setCleanDataCount(cleanData.size());
//            log.info("清洗完成，有效数据: {} 条", cleanData.size());
//
//            // 3. 构建层级树
//            log.info("=== 阶段3: 构建层级树 ===");
//            Map<String, OrgNode> treeNodes = buildTree(cleanData);
//            result.setTreeNodeCount(treeNodes.size());
//            log.info("层级树构建完成，节点数: {}", treeNodes.size());
//
//            // 4. 分配ID并插入数据库
//            log.info("=== 阶段4: 数据库操作 ===");
//            insertToDatabase(treeNodes, result);
//            log.info("数据库操作完成");
//
//            result.setEndTime(LocalDateTime.now());
//            result.setSuccess(true);
//
//            log.info("组织架构数据同步完成: {}", result);
//            return result;
//
//        } catch (Exception e) {
//            log.error("组织架构数据同步失败", e);
//            result.setEndTime(LocalDateTime.now());
//            result.setSuccess(false);
//            result.setErrorMessage(e.getMessage());
//            throw new RuntimeException("数据同步失败: " + e.getMessage(), e);
//        }
//    }
//
//    /**
//     * 解析SQL文件
//     */
//    private List<DepartmentData> parseSqlFile(String filePath) throws IOException {
//        List<String> lines = Files.readAllLines(Paths.get(filePath), StandardCharsets.UTF_8);
//        List<DepartmentData> result = new ArrayList<>();
//
//        Pattern pattern = Pattern.compile(
//            "INSERT INTO `department_sync_test` VALUES \\(" +
//            "'([^']*)', '([^']*)', '([^']*)', '([^']*)', '([^']*)', '([^']*)', " +
//            "'([^']*)', '([^']*)', '([^']*)', '([^']*)', '([^']*)'\\)"
//        );
//
//        for (String line : lines) {
//            if (line.trim().startsWith("INSERT INTO `department_sync_test`")) {
//                Matcher matcher = pattern.matcher(line);
//                if (matcher.find()) {
//                    DepartmentData data = new DepartmentData();
//                    data.setOrgCode(matcher.group(1));
//                    data.setOrgName(matcher.group(2));
//                    data.setParentCode(matcher.group(3));
//                    data.setFullName(matcher.group(4));
//                    data.setUserPredef14(matcher.group(7));
//
//                    if (!"D".equals(data.getUserPredef14()) &&
//                        StringUtils.hasText(data.getFullName())) {
//                        result.add(data);
//                    }
//                }
//            }
//        }
//
//        return result;
//    }
//
//    /**
//     * 数据清洗
//     */
//    private List<DepartmentData> cleanData(List<DepartmentData> rawData) {
//        Map<String, DepartmentData> uniqueData = new LinkedHashMap<>();
//
//        for (DepartmentData data : rawData) {
//            String fullName = data.getFullName().trim();
//            if (!uniqueData.containsKey(fullName)) {
//                uniqueData.put(fullName, data);
//            }
//        }
//
//        return new ArrayList<>(uniqueData.values());
//    }
//
//    /**
//     * 构建层级树
//     */
//    private Map<String, OrgNode> buildTree(List<DepartmentData> cleanData) {
//        Map<String, OrgNode> allNodes = new LinkedHashMap<>();
//
//        for (DepartmentData data : cleanData) {
//            String fullName = data.getFullName();
//            List<String> hierarchy = parseHierarchy(fullName);
//
//            OrgNode currentParent = null;
//
//            for (int i = 0; i < hierarchy.size(); i++) {
//                String levelName = hierarchy.get(i);
//
//                OrgNode node = allNodes.get(levelName);
//                if (node == null) {
//                    node = new OrgNode();
//                    node.setOrganName(levelName);
//                    node.setFullName((i == hierarchy.size() - 1) ? fullName : levelName);
//                    node.setLevel(i + 1);
//                    node.setChildren(new ArrayList<>());
//
//                    allNodes.put(levelName, node);
//
//                    if (currentParent != null) {
//                        currentParent.getChildren().add(node);
//                        node.setParent(currentParent);
//                    }
//                }
//
//                currentParent = node;
//            }
//        }
//
//        return allNodes;
//    }
//
//    /**
//     * 解析层级结构
//     */
//    private List<String> parseHierarchy(String fullName) {
//        if (LEVEL_1_DEPARTMENTS.contains(fullName)) {
//            return Collections.singletonList(fullName);
//        }
//
//        List<String> levels = new ArrayList<>();
//        String remaining = fullName;
//
//        for (String keyword : HIERARCHY_KEYWORDS) {
//            int pos = remaining.indexOf(keyword);
//            if (pos > 0) {
//                String levelName = remaining.substring(0, pos + keyword.length());
//                levels.add(levelName);
//                remaining = remaining.substring(pos + keyword.length());
//
//                if (remaining.isEmpty()) {
//                    break;
//                }
//            }
//        }
//
//        if (!remaining.isEmpty() || levels.isEmpty()) {
//            levels.add(fullName);
//        }
//
//        return levels;
//    }
//
//    /**
//     * 插入数据库
//     */
//    private void insertToDatabase(Map<String, OrgNode> treeNodes, SyncResult result) {
//        // 删除现有同步数据
//        int deletedCount = orgStructureMapper.deleteByDataSource(2);
//        result.setDeletedCount(deletedCount);
//        log.info("删除现有同步数据: {} 条", deletedCount);
//
//        // 获取最大ID
//        Long maxId = orgStructureMapper.getMaxId();
//        AtomicLong currentId = new AtomicLong(Math.max(maxId != null ? maxId : 0L, 2000000000L));
//
//        // 分配ID并插入
//        List<OrgNode> insertNodes = new ArrayList<>();
//        List<OrgNode> rootNodes = treeNodes.values().stream()
//            .filter(node -> node.getParent() == null)
//            .sorted(Comparator.comparing(OrgNode::getOrganName))
//            .collect(Collectors.toList());
//
//        for (OrgNode root : rootNodes) {
//            assignIds(root, currentId, insertNodes);
//        }
//
//        // 批量插入
//        int batchSize = 100;
//        int insertedCount = 0;
//
//        for (int i = 0; i < insertNodes.size(); i += batchSize) {
//            int endIndex = Math.min(i + batchSize, insertNodes.size());
//            List<OrgNode> batch = insertNodes.subList(i, endIndex);
//
//            for (OrgNode node : batch) {
//                orgStructureMapper.insertSyncData(
//                    node.getId(),
//                    node.getOrganName(),
//                    node.getPreId(),
//                    1, // orderInfo
//                    false, // isDel
//                    LocalDateTime.now(),
//                    LocalDateTime.now(),
//                    2 // dataSource
//                );
//                insertedCount++;
//            }
//
//            log.info("批量插入进度: {}/{}", insertedCount, insertNodes.size());
//        }
//
//        result.setInsertedCount(insertedCount);
//        result.setFinalCount(orgStructureMapper.countByDataSource(2));
//    }
//
//    /**
//     * 递归分配ID
//     */
//    private void assignIds(OrgNode node, AtomicLong currentId, List<OrgNode> insertNodes) {
//        node.setId(currentId.incrementAndGet());
//        node.setPreId(node.getParent() != null ? node.getParent().getId() : 0L);
//
//        insertNodes.add(node);
//
//        node.getChildren().sort(Comparator.comparing(OrgNode::getOrganName));
//        for (OrgNode child : node.getChildren()) {
//            assignIds(child, currentId, insertNodes);
//        }
//    }
//
//    // 内部数据类
//    @Data
//    public static class DepartmentData {
//        private String orgCode;
//        private String orgName;
//        private String parentCode;
//        private String fullName;
//        private String userPredef14;
//    }
//
//    @Data
//    public static class OrgNode {
//        private Long id;
//        private String organName;
//        private String fullName;
//        private Long preId;
//        private Integer level;
//        private List<OrgNode> children;
//        private OrgNode parent;
//    }
//
//    @Data
//    public static class SyncResult {
//        private LocalDateTime startTime;
//        private LocalDateTime endTime;
//        private boolean success;
//        private String errorMessage;
//        private int rawDataCount;
//        private int cleanDataCount;
//        private int treeNodeCount;
//        private int deletedCount;
//        private int insertedCount;
//        private int finalCount;
//
//        @Override
//        public String toString() {
//            return String.format(
//                "SyncResult{success=%s, rawData=%d, cleanData=%d, inserted=%d, final=%d}",
//                success, rawDataCount, cleanDataCount, insertedCount, finalCount
//            );
//        }
//    }
//}
