#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成带正确层级关系的部门SQL
参考HR_DEPARTMENT.csv的层级结构来创建部门
"""

import csv
import re
from collections import defaultdict

def load_hr_departments():
    """加载HR部门数据，建立层级关系"""
    departments = {}

    with open('HR_DEPARTMENT.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)

        for row in reader:
            dept_id = row['ID']
            dept_name = row['STR_NAME']
            father_id = row['FATHER_ID'] if row['FATHER_ID'] else None
            dept_sn = row['STR_SN']
            dept_level = row['DEPT_LEVEL']
            dept_desc = row['STR_DESC'] if row['STR_DESC'] else ''

            departments[dept_id] = {
                'id': dept_id,
                'name': dept_name,
                'father_id': father_id,
                'sn': dept_sn,
                'level': int(dept_level) if dept_level else 0,
                'desc': dept_desc
            }

    return departments

def parse_existing_orgs():
    """解析现有组织数据"""
    records = []
    name_to_id = {}  # 部门名称到ID的映射

    try:
        with open("t_org_structure.sql", 'r', encoding='utf-8') as f:
            content = f.read()

        insert_pattern = r"INSERT INTO.*?t_org_structure.*?VALUES\s*\((.*?)\);"
        matches = re.findall(insert_pattern, content, re.DOTALL | re.IGNORECASE)

        for match in matches:
            values = []
            parts = match.split(',')
            for part in parts:
                part = part.strip().strip("'\"")
                values.append(part)

            if len(values) >= 8:
                record = {
                    'id': values[0],
                    'organ_name': values[1],
                    'pre_id': values[2] if values[2] != 'NULL' else None,
                    'order_info': values[3] if values[3] != 'NULL' else None,
                    'is_del': values[4],
                }
                records.append(record)

                # 建立名称到ID的映射（只包含未删除的部门）
                if values[4].lower() in ['f', 'false']:
                    name_to_id[values[1]] = values[0]

    except Exception as e:
        print(f"解析现有组织时出错: {e}")
        return [], {}

    return records, name_to_id

def load_validation_results():
    """加载需要处理的用户"""
    no_org_users = []
    
    with open('基于工号的验证结果.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for row in reader:
            if row['验证状态'] == 'no_org_in_db':
                no_org_users.append(row)
    
    return no_org_users

def generate_snowflake_id():
    """生成雪花算法ID"""
    import time
    import random
    
    timestamp = int(time.time() * 1000)
    random_part = random.randint(100000, 999999)
    return int(f"{timestamp}{random_part}")

def infer_parent_from_desc(dept, hr_departments):
    """通过STR_DESC推断父部门"""
    if dept['father_id']:
        return dept['father_id']

    desc = dept['desc']
    if not desc:
        return None

    # 尝试通过描述找到父部门
    # 例如: "炼铁事业部检修厂检修一车间点维一作业区" -> 寻找包含"炼铁事业部检修厂检修一车间"的部门
    for other_id, other_dept in hr_departments.items():
        if other_id == dept['id']:
            continue

        other_desc = other_dept['desc']
        other_name = other_dept['name']

        # 如果当前部门的描述包含另一个部门的名称，且另一个部门的描述是当前描述的前缀
        if (other_name in desc and
            other_desc and
            desc.startswith(other_desc) and
            len(other_desc) < len(desc)):
            return other_id

    return None

def find_department_hierarchy(dept_id, hr_departments, needed_depts):
    """递归查找部门的完整层级路径，使用STR_DESC推断层级"""
    if dept_id not in hr_departments:
        return []

    dept = hr_departments[dept_id]
    path = [dept]

    # 首先尝试使用father_id
    parent_id = dept['father_id']

    # 如果没有father_id，尝试通过STR_DESC推断
    if not parent_id:
        parent_id = infer_parent_from_desc(dept, hr_departments)

    # 如果找到父部门，递归查找
    if parent_id and parent_id in hr_departments:
        parent_path = find_department_hierarchy(parent_id, hr_departments, needed_depts)
        path = parent_path + path

    return path

def analyze_needed_departments(no_org_users, hr_departments):
    """分析需要创建的部门及其层级关系"""
    needed_depts = set()
    dept_users = defaultdict(list)
    
    # 收集所有需要的部门ID
    for user in no_org_users:
        dept_id = user['CSV部门ID']
        dept_name = user['CSV部门名']
        
        # 跳过未知部门，只处理有具体名称的
        if '未知部门' not in dept_name and dept_name.strip():
            needed_depts.add(dept_id)
            dept_users[dept_id].append({
                'account': user['CSV工号'],
                'name': user['CSV用户名'],
                'dept_name': dept_name
            })
    
    # 为每个需要的部门找到完整的层级路径
    all_needed_depts = set()
    dept_hierarchies = {}
    
    for dept_id in needed_depts:
        if dept_id in hr_departments:
            hierarchy = find_department_hierarchy(dept_id, hr_departments, needed_depts)
            dept_hierarchies[dept_id] = hierarchy
            
            # 将整个层级路径上的所有部门都加入需要创建的列表
            for dept in hierarchy:
                all_needed_depts.add(dept['id'])
    
    return all_needed_depts, dept_hierarchies, dept_users

def generate_hierarchical_sql(all_needed_depts, dept_hierarchies, dept_users, hr_departments, existing_orgs, existing_name_to_id):
    """生成带层级关系的部门创建SQL"""
    sql_statements = []
    sql_statements.append("-- 创建带正确层级关系的部门组织")
    sql_statements.append("-- 参考HR_DEPARTMENT.csv的层级结构和STR_DESC推断层级")
    sql_statements.append("-- 跳过权限系统中已存在的部门")
    sql_statements.append("")

    # 获取现有组织的最大order_info
    max_order = 0
    for org in existing_orgs:
        if org['order_info'] and org['order_info'].isdigit():
            max_order = max(max_order, int(org['order_info']))

    # 重新计算每个部门的实际层级（基于推断的父子关系）
    dept_levels = {}
    dept_parents = {}

    for dept_id in all_needed_depts:
        if dept_id in hr_departments:
            dept = hr_departments[dept_id]
            parent_id = dept['father_id']

            # 如果没有father_id，尝试通过STR_DESC推断
            if not parent_id:
                parent_id = infer_parent_from_desc(dept, hr_departments)

            dept_parents[dept_id] = parent_id

            # 计算层级深度
            level = 0
            current_id = dept_id
            visited = set()

            while current_id and current_id not in visited:
                visited.add(current_id)
                parent = dept_parents.get(current_id)
                if parent and parent in all_needed_depts:
                    level += 1
                    current_id = parent
                else:
                    break

            dept_levels[dept_id] = level

    # 按层级排序创建部门（先创建父部门，再创建子部门）
    dept_id_mapping = {}  # HR部门ID -> 权限系统ID的映射
    order_counter = max_order + 1

    # 首先将已存在的部门加入映射
    for dept_id in all_needed_depts:
        if dept_id in hr_departments:
            dept_name = hr_departments[dept_id]['name']
            if dept_name in existing_name_to_id:
                dept_id_mapping[dept_id] = existing_name_to_id[dept_name]

    # 按层级级别排序
    sorted_depts = []
    for dept_id in all_needed_depts:
        if dept_id in hr_departments:
            dept = hr_departments[dept_id]
            level = dept_levels.get(dept_id, 0)
            sorted_depts.append((level, dept_id, dept))

    sorted_depts.sort(key=lambda x: x[0])  # 按层级排序

    created_count = 0
    skipped_count = 0

    for level, dept_id, dept in sorted_depts:
        dept_name = dept['name']

        # 检查部门是否已存在
        if dept_name in existing_name_to_id:
            skipped_count += 1
            sql_statements.append(f"-- 跳过已存在的部门: {dept_name} (ID: {existing_name_to_id[dept_name]})")
            continue

        new_id = generate_snowflake_id()
        dept_id_mapping[dept_id] = new_id
        created_count += 1

        # 确定父部门ID
        pre_id = 'NULL'
        parent_id = dept_parents.get(dept_id)
        if parent_id and parent_id in dept_id_mapping:
            pre_id = str(dept_id_mapping[parent_id])

        user_count = len(dept_users.get(dept_id, []))
        user_info = f", 直接用户数: {user_count}" if user_count > 0 else ""
        parent_info = f", 父部门: {hr_departments.get(parent_id, {}).get('name', 'N/A')}" if parent_id else ""

        sql = f"""INSERT INTO "public"."t_org_structure"
(id, organ_name, pre_id, order_info, is_del, create_time, modify_time, data_source)
VALUES ({new_id}, '{dept_name}', {pre_id}, {order_counter}, false, NOW(), NOW(), 2);"""

        sql_statements.append(sql)
        sql_statements.append(f"-- 部门: {dept_name}, 层级: {level}, 编号: {dept['sn']}{user_info}{parent_info}")
        sql_statements.append("")

        order_counter += 1

    sql_statements.append(f"-- 统计: 创建了{created_count}个新部门, 跳过了{skipped_count}个已存在的部门")
    sql_statements.append("")

    return sql_statements, dept_id_mapping

def generate_user_updates(no_org_users, dept_id_mapping):
    """生成用户更新SQL"""
    sql_statements = []
    sql_statements.append("-- 更新用户的部门关联（基于层级关系）")
    sql_statements.append("")
    
    updated_count = 0
    
    for user in no_org_users:
        dept_id = user['CSV部门ID']
        dept_name = user['CSV部门名']
        account = user['CSV工号']
        user_name = user['CSV用户名']
        
        # 只处理有具体部门名称且已创建的部门
        if '未知部门' not in dept_name and dept_id in dept_id_mapping:
            new_org_id = dept_id_mapping[dept_id]
            
            sql = f"""UPDATE "public"."t_user" 
SET organ_affiliation = {new_org_id}, modify_time = NOW() 
WHERE account = '{account}' AND is_del = false;"""
            
            sql_statements.append(f"-- 用户: {user_name} ({account}) -> {dept_name}")
            sql_statements.append(sql)
            sql_statements.append("")
            
            updated_count += 1
    
    return sql_statements, updated_count

def generate_verification_sql():
    """生成验证SQL"""
    sql_statements = []
    sql_statements.append("-- 验证SQL - 检查层级关系和修正结果")
    sql_statements.append("")
    sql_statements.append("-- 1. 查看新创建的部门层级结构")
    sql_statements.append("""WITH RECURSIVE dept_tree AS (
    -- 根节点
    SELECT id, organ_name, pre_id, order_info, 0 as level, organ_name as path
    FROM "public"."t_org_structure" 
    WHERE data_source = 2 AND is_del = false AND pre_id IS NULL
    
    UNION ALL
    
    -- 递归查找子节点
    SELECT o.id, o.organ_name, o.pre_id, o.order_info, dt.level + 1, 
           dt.path || ' -> ' || o.organ_name
    FROM "public"."t_org_structure" o
    INNER JOIN dept_tree dt ON o.pre_id = dt.id
    WHERE o.data_source = 2 AND o.is_del = false
)
SELECT level, organ_name, path FROM dept_tree ORDER BY level, order_info;""")
    sql_statements.append("")
    sql_statements.append("-- 2. 检查用户部门关联统计")
    sql_statements.append("""SELECT 
    CASE 
        WHEN organ_affiliation IS NULL THEN '无部门'
        ELSE '有部门'
    END as dept_status,
    COUNT(*) as user_count
FROM "public"."t_user" 
WHERE is_del = false 
GROUP BY (organ_affiliation IS NULL);""")
    
    return sql_statements

def main():
    """主函数"""
    print("=== 生成带层级关系的部门SQL ===")
    
    # 加载HR部门数据
    print("加载HR部门数据...")
    hr_departments = load_hr_departments()
    print(f"HR部门总数: {len(hr_departments)}")
    
    # 解析现有组织
    print("解析现有组织...")
    existing_orgs, existing_name_to_id = parse_existing_orgs()
    print(f"现有组织数: {len(existing_orgs)}")
    print(f"现有部门名称: {len(existing_name_to_id)}")
    
    # 加载需要处理的用户
    print("加载需要处理的用户...")
    no_org_users = load_validation_results()
    print(f"需要处理的用户: {len(no_org_users)}")
    
    # 分析需要创建的部门
    print("分析部门层级关系...")
    all_needed_depts, dept_hierarchies, dept_users = analyze_needed_departments(no_org_users, hr_departments)
    print(f"需要创建的部门总数: {len(all_needed_depts)}")
    print(f"有直接用户的部门: {len(dept_users)}")
    
    # 生成SQL
    all_sql = []
    
    # 1. 生成部门创建SQL
    dept_creation_sql, dept_id_mapping = generate_hierarchical_sql(
        all_needed_depts, dept_hierarchies, dept_users, hr_departments, existing_orgs, existing_name_to_id)
    all_sql.extend(dept_creation_sql)
    
    # 2. 生成用户更新SQL
    user_update_sql, updated_count = generate_user_updates(no_org_users, dept_id_mapping)
    all_sql.extend(user_update_sql)
    
    # 3. 生成验证SQL
    verification_sql = generate_verification_sql()
    all_sql.extend(verification_sql)
    
    # 保存SQL文件
    output_file = "hierarchical_department_fix.sql"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(all_sql))
    
    print(f"\n=== 生成完成 ===")
    print(f"SQL文件: {output_file}")
    print(f"创建部门总数: {len(all_needed_depts)}")
    print(f"更新用户数: {updated_count}")
    
    # 显示层级结构示例
    print(f"\n=== 部门层级示例 ===")
    for dept_id, hierarchy in list(dept_hierarchies.items())[:5]:
        path = " -> ".join([d['name'] for d in hierarchy])
        user_count = len(dept_users.get(dept_id, []))
        print(f"{path} ({user_count}个用户)")

if __name__ == "__main__":
    main()
