@echo off
echo === ERP部门数据获取工具编译和运行脚本 ===

REM 设置编码
chcp 65001

REM 检查Java环境
echo 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请确保已安装JDK
    pause
    exit /b 1
)

REM 编译Java程序
echo.
echo 编译Java程序...
javac -encoding UTF-8 ErpDepartmentFetcher.java
if %errorlevel% neq 0 (
    echo 错误: 编译失败
    pause
    exit /b 1
)

echo 编译成功！

REM 运行程序
echo.
echo 运行程序...
java -Dfile.encoding=UTF-8 ErpDepartmentFetcher

echo.
echo 程序执行完成！
pause
