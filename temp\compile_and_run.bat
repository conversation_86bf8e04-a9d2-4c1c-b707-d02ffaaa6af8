@echo off
echo === ERP部门数据获取工具编译和运行脚本 ===

REM 设置编码
chcp 65001

REM 检查Java环境
echo 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请确保已安装JDK
    pause
    exit /b 1
)

REM 检查MySQL JDBC驱动
echo.
echo 检查MySQL JDBC驱动...
if not exist "mysql-connector-java-*.jar" (
    echo 警告: 未找到MySQL JDBC驱动jar包
    echo 请下载mysql-connector-java-x.x.x.jar并放在当前目录
    echo 下载地址: https://dev.mysql.com/downloads/connector/j/
    echo 或者使用Maven: https://mvnrepository.com/artifact/mysql/mysql-connector-java
    echo.
    echo 继续编译（运行时可能会出错）...
)

REM 编译Java程序
echo.
echo 编译Java程序...
javac -encoding UTF-8 ErpDepartmentFetcher.java
if %errorlevel% neq 0 (
    echo 错误: 编译失败
    pause
    exit /b 1
)

echo 编译成功！

REM 运行程序
echo.
echo 运行程序...
if exist "mysql-connector-java-*.jar" (
    for %%f in (mysql-connector-java-*.jar) do (
        echo 使用MySQL驱动: %%f
        java -Dfile.encoding=UTF-8 -cp ".;%%f" ErpDepartmentFetcher
        goto :end
    )
) else (
    echo 尝试不使用外部驱动运行（可能失败）...
    java -Dfile.encoding=UTF-8 ErpDepartmentFetcher
)

:end

echo.
echo 程序执行完成！
pause
