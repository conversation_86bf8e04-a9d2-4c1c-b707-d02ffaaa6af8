# 用户导入 API 调用指南

您可以使用 Postman 或类似的 API 工具来调用用户导入接口。以下是详细的配置步骤：

## 1. 请求设置

- **方法 (Method)**: `POST`
- **URL**: `http://localhost:8080/api/temp/users/upload` (请根据您的实际端口号进行调整)

## 2. 请求头 (Headers)

- **Key**: `Content-Type`
- **Value**: `multipart/form-data`

## 3. 请求体 (Body)

1.  选择 `form-data` 格式。
2.  添加一个新的键值对：
    - **Key**: `file`
    - **类型 (Type)**: 选择 `File`
    - **值 (Value)**: 点击 "Select Files"，然后选择您本地的 `HR_USER.xlsx` 文件。

## 4. 发送请求

完成以上配置后，点击 "Send" 按钮即可发送请求。您将在响应体中看到导入的结果。

---

## VS Code REST Client 示例

如果您在 VS Code 中使用 [REST Client](https://marketplace.visualstudio.com/items?itemName=humao.rest-client) 插件，可以创建一个 `.http` 文件，并使用以下内容直接发送请求：

```http
### Import Users from Excel

# @name importUsers
POST http://localhost:8080/api/temp/users/upload
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="HR_USER.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./HR_USER.xlsx

------WebKitFormBoundary7MA4YWxkTrZu0gW--
```

**注意**:
- 请确保 `HR_USER.xlsx` 文件与您的 `.http` 文件位于同一目录下，或者使用正确的文件路径。
- `boundary` 的值可以任意设置，但必须在请求头和请求体中保持一致。