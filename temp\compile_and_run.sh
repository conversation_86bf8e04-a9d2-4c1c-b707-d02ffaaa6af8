#!/bin/bash

echo "=== ERP部门数据获取工具编译和运行脚本 ==="

# 检查Java环境
echo "检查Java环境..."
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境，请确保已安装JDK"
    exit 1
fi

java -version

# 编译Java程序
echo ""
echo "编译Java程序..."
javac -encoding UTF-8 ErpDepartmentFetcher.java
if [ $? -ne 0 ]; then
    echo "错误: 编译失败"
    exit 1
fi

echo "编译成功！"

# 运行程序
echo ""
echo "运行程序..."
java -Dfile.encoding=UTF-8 ErpDepartmentFetcher

echo ""
echo "程序执行完成！"
