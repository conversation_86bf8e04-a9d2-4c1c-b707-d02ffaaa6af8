package com.dfit.percode.util;

import com.dfit.percode.temp.userimport.dto.UserImportDto;
import org.apache.poi.ss.usermodel.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

public class UserImportUtil {

    public static List<UserImportDto> excelToUserImportDto(InputStream inputStream) {
        try {
            Workbook workbook = WorkbookFactory.create(inputStream);
            Sheet sheet = workbook.getSheetAt(0);
            List<UserImportDto> list = new ArrayList<>();
            // i=1 是因为第一行是表头
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }
                UserImportDto userImportDto = new UserImportDto();
                userImportDto.setId(getStringCellValue(row.getCell(0)));
                userImportDto.setUserNo(getStringCellValue(row.getCell(1)));
                userImportDto.setName(getStringCellValue(row.getCell(2)));
                userImportDto.setPhone(getStringCellValue(row.getCell(3)));
                userImportDto.setCreateUserNo(getStringCellValue(row.getCell(4)));
                userImportDto.setCreateDateTime(getStringCellValue(row.getCell(5)));
                userImportDto.setUpdateUserNo(getStringCellValue(row.getCell(6)));
                userImportDto.setUpdateDateTime(getStringCellValue(row.getCell(7)));
                userImportDto.setCompanyId(getStringCellValue(row.getCell(8)));
                userImportDto.setDeptId(getStringCellValue(row.getCell(9)));
                userImportDto.setTenantId(getStringCellValue(row.getCell(10)));
                list.add(userImportDto);
            }
            workbook.close();
            return list;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static String getStringCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        cell.setCellType(CellType.STRING);
        return cell.getStringCellValue();
    }
}