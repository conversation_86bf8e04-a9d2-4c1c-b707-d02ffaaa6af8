#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析988个"未知部门"用户的部门ID模式
"""

import csv
import re
from collections import Counter

def main():
    # 读取验证结果，找出未知部门的用户
    unknown_dept_users = []
    
    with open('基于工号的验证结果_修正版.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for row in reader:
            if row['验证状态'] == 'no_org_in_db' and '未知部门' in row['HR部门']:
                # 提取部门ID
                dept_desc = row['HR部门']
                # 从"未知部门(ZB030000...)"中提取部门ID
                match = re.search(r'未知部门\(([^)]+)\)', dept_desc)
                if match:
                    dept_id = match.group(1).replace('...', '')
                    unknown_dept_users.append({
                        'account': row['工号'],
                        'name': row['HR姓名'],
                        'dept_id': dept_id,
                        'phone': row['HR手机']
                    })

    print(f'未知部门用户总数: {len(unknown_dept_users)}')
    
    # 分析部门ID模式
    dept_ids = [user['dept_id'] for user in unknown_dept_users]
    dept_id_counts = Counter(dept_ids)
    
    print(f'\n部门ID统计 (前20个):')
    for dept_id, count in dept_id_counts.most_common(20):
        print(f'  {dept_id}: {count}个用户')
    
    # 分析ID前缀模式
    prefixes = []
    for dept_id in dept_ids:
        if len(dept_id) >= 2:
            prefixes.append(dept_id[:2])
    
    prefix_counts = Counter(prefixes)
    print(f'\n部门ID前缀统计:')
    for prefix, count in prefix_counts.most_common():
        print(f'  {prefix}*: {count}个用户')
    
    # 分析ID长度
    lengths = [len(dept_id) for dept_id in dept_ids]
    length_counts = Counter(lengths)
    print(f'\n部门ID长度统计:')
    for length, count in length_counts.most_common():
        print(f'  {length}位: {count}个用户')
    
    # 显示一些具体例子
    print(f'\n具体例子 (前10个):')
    for i, user in enumerate(unknown_dept_users[:10]):
        print(f'  {user["name"]} ({user["account"]}) -> {user["dept_id"]}')
    
    # 检查是否有重复的部门ID
    unique_dept_ids = len(set(dept_ids))
    print(f'\n唯一部门ID数量: {unique_dept_ids}')
    print(f'总用户数: {len(unknown_dept_users)}')
    print(f'平均每个部门ID: {len(unknown_dept_users)/unique_dept_ids:.1f}个用户')

if __name__ == "__main__":
    main()
