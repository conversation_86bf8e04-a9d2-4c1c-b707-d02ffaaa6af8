2025-07-30 11:06:36.021 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 1.8.0_442 on DESKTOP-UKI346C with PID 32400 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-07-30 11:06:36.032 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-07-30 11:06:36.060 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-30 11:06:39.423 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 11:06:39.430 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-30 11:06:39.531 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 84 ms. Found 0 Elasticsearch repository interfaces.
2025-07-30 11:06:39.547 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 11:06:39.549 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-30 11:06:39.606 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 55 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-30 11:06:39.657 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 11:06:39.680 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-30 11:06:39.751 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 38 ms. Found 0 JPA repository interfaces.
2025-07-30 11:06:39.849 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 11:06:39.853 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 11:06:40.042 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 68 ms. Found 0 Redis repository interfaces.
2025-07-30 11:06:42.294 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-07-30 11:06:42.324 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-07-30 11:06:42.325 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 11:06:42.326 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-30 11:06:42.549 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 11:06:42.550 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 6329 ms
2025-07-30 11:06:43.347 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-30 11:06:43.804 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-30 11:06:46.074 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-30 11:06:46.726 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-07-30 11:06:49.323 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-30 11:06:50.651 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-07-30 11:06:53.210 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-30 11:06:53.356 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-30 11:06:53.426 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 超级管理员功能已启用
2025-07-30 11:06:53.427 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户ID数量: 2
2025-07-30 11:06:53.436 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员登录账号数量: 2
2025-07-30 11:06:53.436 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户名数量: 2
2025-07-30 11:07:06.396 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-30 11:07:20.171 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-07-30 11:07:20.263 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-07-30 11:07:24.853 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 50.899 seconds (JVM running for 64.958)
2025-07-30 11:13:16.575 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 11:13:16.575 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-30 11:13:16.594 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 14 ms
2025-07-30 11:13:19.442 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始批量查询用户详情，用户ID数量: 5
2025-07-30 11:13:20.531 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 批量查询到用户数量: 2
2025-07-30 11:13:20.666 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 批量查询用户详情完成
2025-07-30 11:16:02.368 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-30 11:16:02.385 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
