#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成正确的部门关联修正SQL
基于实际的表结构生成符合权限系统的SQL语句
"""

import csv
import re
from collections import defaultdict

def parse_sql_insert_statements(sql_file):
    """解析SQL文件中的INSERT语句，获取现有组织数据"""
    records = []
    
    try:
        with open(sql_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 查找INSERT语句的模式
        insert_pattern = r"INSERT INTO.*?t_org_structure.*?VALUES\s*\((.*?)\);"
        matches = re.findall(insert_pattern, content, re.DOTALL | re.IGNORECASE)
        
        for match in matches:
            # 简单解析VALUES中的数据
            values = []
            parts = match.split(',')
            for part in parts:
                part = part.strip().strip("'\"")
                values.append(part)
            
            if len(values) >= 8:
                records.append({
                    'id': values[0],
                    'organ_name': values[1],
                    'pre_id': values[2] if values[2] != 'NULL' else None,
                    'order_info': values[3] if values[3] != 'NULL' else None,
                    'is_del': values[4],
                    'create_time': values[5],
                    'modify_time': values[6],
                    'data_source': values[7] if len(values) > 7 else '1'
                })
                
    except Exception as e:
        print(f"解析SQL文件时出错: {e}")
        return []
    
    return records

def generate_snowflake_id():
    """生成类似雪花算法的ID（简化版）"""
    import time
    import random
    
    # 使用当前时间戳和随机数生成类似雪花算法的ID
    timestamp = int(time.time() * 1000)
    random_part = random.randint(100000, 999999)
    return int(f"{timestamp}{random_part}")

def load_validation_results():
    """加载验证结果"""
    no_org_users = []
    
    with open('基于工号的验证结果.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for row in reader:
            if row['验证状态'] == 'no_org_in_db':
                no_org_users.append(row)
    
    return no_org_users

def analyze_departments(no_org_users):
    """分析需要创建的部门"""
    dept_info = {}
    
    for user in no_org_users:
        dept_name = user['CSV部门名']
        dept_id = user['CSV部门ID']
        
        # 跳过未知部门，只处理有具体名称的部门
        if '未知部门' not in dept_name and dept_name.strip():
            if dept_id not in dept_info:
                dept_info[dept_id] = {
                    'name': dept_name,
                    'users': []
                }
            dept_info[dept_id]['users'].append({
                'account': user['CSV工号'],
                'name': user['CSV用户名']
            })
    
    return dept_info

def generate_department_creation_sql(dept_info, existing_orgs):
    """生成部门创建SQL"""
    sql_statements = []
    sql_statements.append("-- 创建缺失的部门组织（基于实际表结构）")
    sql_statements.append("-- 表结构: t_org_structure")
    sql_statements.append("-- 字段: id(int8), organ_name, pre_id, order_info, is_del, create_time, modify_time, data_source")
    sql_statements.append("")
    
    # 获取现有组织的最大order_info
    max_order = 0
    for org in existing_orgs:
        if org['order_info'] and org['order_info'].isdigit():
            max_order = max(max_order, int(org['order_info']))
    
    dept_id_mapping = {}
    order_counter = max_order + 1
    
    for dept_id, info in dept_info.items():
        dept_name = info['name']
        new_id = generate_snowflake_id()
        dept_id_mapping[dept_id] = new_id
        
        sql = f"""INSERT INTO "public"."t_org_structure" 
(id, organ_name, pre_id, order_info, is_del, create_time, modify_time, data_source) 
VALUES ({new_id}, '{dept_name}', NULL, {order_counter}, false, NOW(), NOW(), 2);"""
        
        sql_statements.append(sql)
        sql_statements.append(f"-- 部门: {dept_name}, 用户数: {len(info['users'])}")
        sql_statements.append("")
        
        order_counter += 1
    
    return sql_statements, dept_id_mapping

def generate_user_update_sql(no_org_users, dept_id_mapping):
    """生成用户更新SQL"""
    sql_statements = []
    sql_statements.append("-- 更新用户的部门关联")
    sql_statements.append("-- 使用新创建的部门ID更新用户的organ_affiliation字段")
    sql_statements.append("")
    
    updated_count = 0
    
    for user in no_org_users:
        dept_name = user['CSV部门名']
        dept_id = user['CSV部门ID']
        account = user['CSV工号']
        user_name = user['CSV用户名']
        
        # 只处理有具体部门名称且已创建的部门
        if '未知部门' not in dept_name and dept_id in dept_id_mapping:
            new_org_id = dept_id_mapping[dept_id]
            
            sql = f"""UPDATE "public"."t_user" 
SET organ_affiliation = {new_org_id}, modify_time = NOW() 
WHERE account = '{account}' AND is_del = false;"""
            
            sql_statements.append(f"-- 用户: {user_name} ({account}) -> {dept_name}")
            sql_statements.append(sql)
            sql_statements.append("")
            
            updated_count += 1
    
    return sql_statements, updated_count

def generate_verification_sql():
    """生成验证SQL"""
    sql_statements = []
    sql_statements.append("-- 验证SQL - 检查修正结果")
    sql_statements.append("")
    sql_statements.append("-- 1. 检查仍然没有部门关联的用户数量")
    sql_statements.append('SELECT COUNT(*) as users_without_dept FROM "public"."t_user" WHERE organ_affiliation IS NULL AND is_del = false;')
    sql_statements.append("")
    sql_statements.append("-- 2. 检查新创建的部门数量（data_source=2的部门）")
    sql_statements.append('SELECT COUNT(*) as new_departments FROM "public"."t_org_structure" WHERE data_source = 2 AND is_del = false;')
    sql_statements.append("")
    sql_statements.append("-- 3. 检查部门关联统计")
    sql_statements.append("""SELECT 
    CASE 
        WHEN organ_affiliation IS NULL THEN '无部门'
        ELSE '有部门'
    END as dept_status,
    COUNT(*) as user_count
FROM "public"."t_user" 
WHERE is_del = false 
GROUP BY (organ_affiliation IS NULL);""")
    sql_statements.append("")
    sql_statements.append("-- 4. 查看新创建的部门列表")
    sql_statements.append('SELECT id, organ_name, order_info FROM "public"."t_org_structure" WHERE data_source = 2 AND is_del = false ORDER BY order_info;')
    
    return sql_statements

def main():
    """主函数"""
    print("=== 生成正确的部门关联修正SQL ===")
    
    # 解析现有组织数据
    print("解析现有组织数据...")
    existing_orgs = parse_sql_insert_statements("t_org_structure.sql")
    print(f"现有组织数量: {len(existing_orgs)}")
    
    # 加载验证结果
    print("加载验证结果...")
    no_org_users = load_validation_results()
    print(f"需要处理的用户: {len(no_org_users)}")
    
    # 分析需要创建的部门
    print("分析需要创建的部门...")
    dept_info = analyze_departments(no_org_users)
    print(f"需要创建的部门: {len(dept_info)}")
    
    # 生成SQL
    all_sql = []
    
    # 1. 生成部门创建SQL
    dept_creation_sql, dept_id_mapping = generate_department_creation_sql(dept_info, existing_orgs)
    all_sql.extend(dept_creation_sql)
    
    # 2. 生成用户更新SQL
    user_update_sql, updated_count = generate_user_update_sql(no_org_users, dept_id_mapping)
    all_sql.extend(user_update_sql)
    
    # 3. 生成验证SQL
    verification_sql = generate_verification_sql()
    all_sql.extend(verification_sql)
    
    # 保存SQL文件
    output_file = "correct_department_fix.sql"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(all_sql))
    
    print(f"\n=== 生成完成 ===")
    print(f"SQL文件已保存: {output_file}")
    print(f"创建部门数量: {len(dept_info)}")
    print(f"更新用户数量: {updated_count}")
    print(f"剩余未处理用户: {len(no_org_users) - updated_count}")
    
    print(f"\n=== 部门列表 ===")
    for dept_id, info in dept_info.items():
        print(f"- {info['name']}: {len(info['users'])}个用户")

if __name__ == "__main__":
    main()
