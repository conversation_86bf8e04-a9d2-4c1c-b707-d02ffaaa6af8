package com.dfit.percode.temp.userimport.service;

import com.dfit.percode.mapper.TUserMapper;
import com.dfit.percode.temp.userimport.service.impl.UserImportServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class UserImportServiceTest {

    @InjectMocks
    private UserImportServiceImpl userImportService;

    @Mock
    private TUserMapper tUserMapper;

    @Test
    public void testImportUsers() throws Exception {
        // 准备模拟数据
        String excelContent = "ID,USER_NO,NAME,PHONE,CREATE_USER_NO,CREATE_DATE_TIME,UPDATE_USER_NO,UPDATE_DATE_TIME,COMPANY_ID,DEPT_ID,TENANT_ID\n" +
                "1,001,Test User,1234567890,admin,2025-01-01 10:00:00,admin,2025-01-01 10:00:00,1,DeptA,1";
        InputStream inputStream = new ByteArrayInputStream(excelContent.getBytes());
        MockMultipartFile file = new MockMultipartFile("file", "test.xlsx", "text/plain", inputStream);

        Map<String, String> deptMap = new HashMap<>();
        deptMap.put("DeptA", "101");
        when(tUserMapper.getAllDeptIdAndName()).thenReturn(deptMap);
        when(tUserMapper.selectById(1L)).thenReturn(null);

        // 执行测试
        String result = userImportService.importUsers(file);

        // 验证结果
        assertEquals("导入成功", result);
    }
}