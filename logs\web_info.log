2025-08-02 10:09:25.129 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 1.8.0_442 on DESKTOP-UKI346C with PID 29308 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-08-02 10:09:25.159 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-08-02 10:09:25.310 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-02 10:09:29.116 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-02 10:09:29.125 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-08-02 10:09:29.195 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 47 ms. Found 0 Elasticsearch repository interfaces.
2025-08-02 10:09:29.207 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-02 10:09:29.208 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-08-02 10:09:29.239 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 29 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-08-02 10:09:29.260 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-02 10:09:29.262 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-02 10:09:29.310 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 27 ms. Found 0 JPA repository interfaces.
2025-08-02 10:09:29.376 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-02 10:09:29.383 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-02 10:09:29.467 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 46 ms. Found 0 Redis repository interfaces.
2025-08-02 10:09:31.295 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-08-02 10:09:31.321 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-08-02 10:09:31.322 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-02 10:09:31.323 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-08-02 10:09:31.488 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-02 10:09:31.488 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 6156 ms
2025-08-02 10:09:31.707 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-08-02 10:09:32.077 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-02 10:09:33.747 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-02 10:09:33.953 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-08-02 10:09:34.673 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-02 10:09:35.285 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-08-02 10:09:36.302 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-02 10:09:36.355 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-02 10:09:36.393 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 超级管理员功能已启用
2025-08-02 10:09:36.394 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户ID数量: 2
2025-08-02 10:09:36.398 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员登录账号数量: 2
2025-08-02 10:09:36.399 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户名数量: 2
2025-08-02 10:09:40.714 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-02 10:09:45.783 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-08-02 10:09:45.815 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-08-02 10:09:47.403 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 25.122 seconds (JVM running for 31.289)
2025-08-02 10:14:37.884 [http-nio-8285-exec-3] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-02 10:14:37.885 [http-nio-8285-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-02 10:14:37.888 [http-nio-8285-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-08-02 10:14:38.204 [http-nio-8285-exec-3] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /api/temp/users/upload, Method: POST, IP: 0:0:0:0:0:0:0:1
2025-08-02 10:14:39.011 [http-nio-8285-exec-3] WARN  com.dfit.percode.interceptor.HybridAuthInterceptor - 混合认证失败：缺少Authorization头, URI: /api/temp/users/upload
2025-08-02 10:15:30.987 [http-nio-8285-exec-2] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: 1001
2025-08-02 10:15:33.935 [http-nio-8285-exec-2] INFO  com.dfit.percode.controller.AuthController - 用户登录成功（混合模式），账号: 1001, 用户ID: 1936640367617249280, Sa-Token会话: 已建立, 自定义JWT: 已生成, 耗时: 2948ms
2025-08-02 10:15:52.294 [http-nio-8285-exec-4] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /api/temp/users/upload, Method: POST, IP: 0:0:0:0:0:0:0:1
2025-08-02 10:15:52.853 [http-nio-8285-exec-4] INFO  c.d.p.t.u.service.impl.UserImportServiceImpl - 开始处理用户导入...
2025-08-02 10:16:00.076 [http-nio-8285-exec-4] INFO  c.d.p.t.u.service.impl.UserImportServiceImpl - 从Excel中解析出 11288 个用户
2025-08-02 10:16:00.529 [http-nio-8285-exec-4] WARN  com.dfit.percode.listener.UserActionInterceptor - 慢请求警告 - URI: /api/temp/users/upload, 处理时间: 8235ms
2025-08-02 10:18:12.289 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-02 10:18:12.303 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-08-02 10:18:27.136 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 1.8.0_442 on DESKTOP-UKI346C with PID 11196 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-08-02 10:18:27.139 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-08-02 10:18:27.143 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-02 10:18:29.136 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-02 10:18:29.140 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-08-02 10:18:29.186 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 Elasticsearch repository interfaces.
2025-08-02 10:18:29.191 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-02 10:18:29.192 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-08-02 10:18:29.205 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-08-02 10:18:29.216 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-02 10:18:29.217 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-02 10:18:29.238 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 JPA repository interfaces.
2025-08-02 10:18:29.266 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-02 10:18:29.270 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-02 10:18:29.300 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-08-02 10:18:30.668 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-08-02 10:18:30.685 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-08-02 10:18:30.685 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-02 10:18:30.686 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-08-02 10:18:30.812 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-02 10:18:30.813 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3567 ms
2025-08-02 10:18:31.044 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-08-02 10:18:31.385 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-02 10:18:32.778 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-02 10:18:32.932 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-08-02 10:18:33.340 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-02 10:18:33.767 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-08-02 10:18:34.546 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-02 10:18:34.582 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-02 10:18:34.619 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 超级管理员功能已启用
2025-08-02 10:18:34.620 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户ID数量: 2
2025-08-02 10:18:34.624 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员登录账号数量: 2
2025-08-02 10:18:34.624 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户名数量: 2
2025-08-02 10:18:39.040 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-02 10:18:43.872 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-08-02 10:18:43.910 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-08-02 10:18:45.330 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 19.708 seconds (JVM running for 22.84)
2025-08-02 10:18:54.051 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-02 10:18:54.051 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-02 10:18:54.053 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-02 10:18:54.434 [http-nio-8285-exec-1] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /api/temp/users/upload, Method: POST, IP: 0:0:0:0:0:0:0:1
2025-08-02 10:18:56.354 [http-nio-8285-exec-1] INFO  c.d.p.t.u.service.impl.UserImportServiceImpl - 开始处理用户导入...
2025-08-02 10:19:01.190 [http-nio-8285-exec-1] INFO  c.d.p.t.u.service.impl.UserImportServiceImpl - 从Excel中解析出 11288 个用户
2025-08-02 10:19:01.994 [http-nio-8285-exec-1] WARN  com.dfit.percode.listener.UserActionInterceptor - 慢请求警告 - URI: /api/temp/users/upload, 处理时间: 7559ms
2025-08-02 10:42:22.446 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-02 10:42:22.482 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-08-02 10:42:43.582 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 1.8.0_442 on DESKTOP-UKI346C with PID 33008 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-08-02 10:42:43.585 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-08-02 10:42:43.624 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-02 10:42:47.393 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-02 10:42:47.400 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-08-02 10:42:47.591 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 173 ms. Found 0 Elasticsearch repository interfaces.
2025-08-02 10:42:47.606 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-02 10:42:47.614 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-08-02 10:42:47.641 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 23 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-08-02 10:42:47.776 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-02 10:42:47.778 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-02 10:42:47.854 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 49 ms. Found 0 JPA repository interfaces.
2025-08-02 10:42:47.922 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-02 10:42:47.927 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-02 10:42:47.977 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.
2025-08-02 10:42:49.923 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-08-02 10:42:49.971 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-08-02 10:42:49.973 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-02 10:42:49.974 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-08-02 10:42:50.238 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-02 10:42:50.239 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 6509 ms
2025-08-02 10:42:50.467 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-08-02 10:42:50.770 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-02 10:42:52.207 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-02 10:42:52.441 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-08-02 10:42:53.027 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-02 10:42:53.519 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-08-02 10:42:54.165 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-02 10:42:54.195 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-02 10:42:54.224 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 超级管理员功能已启用
2025-08-02 10:42:54.225 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户ID数量: 2
2025-08-02 10:42:54.228 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员登录账号数量: 2
2025-08-02 10:42:54.228 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户名数量: 2
2025-08-02 10:42:58.252 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-02 10:43:03.221 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-08-02 10:43:03.262 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-08-02 10:43:04.784 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 23.335 seconds (JVM running for 27.005)
2025-08-02 10:43:12.200 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-02 10:43:12.201 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-02 10:43:12.203 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-02 10:43:12.706 [http-nio-8285-exec-2] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /api/temp/users/upload, Method: POST, IP: 0:0:0:0:0:0:0:1
2025-08-02 10:43:14.433 [http-nio-8285-exec-2] INFO  c.d.p.t.u.service.impl.UserImportServiceImpl - 开始处理用户导入...
2025-08-02 10:43:20.159 [http-nio-8285-exec-2] INFO  c.d.p.t.u.service.impl.UserImportServiceImpl - 从Excel中解析出 11288 个用户
2025-08-02 10:43:20.161 [http-nio-8285-exec-2] INFO  c.d.p.t.u.service.impl.UserImportServiceImpl - 加载了 1478 个部门ID映射
2025-08-02 10:43:21.489 [http-nio-8285-exec-2] WARN  com.dfit.percode.listener.UserActionInterceptor - 慢请求警告 - URI: /api/temp/users/upload, 处理时间: 8781ms
2025-08-02 10:45:17.720 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-02 10:45:17.735 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-08-02 10:45:32.369 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 1.8.0_442 on DESKTOP-UKI346C with PID 17240 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-08-02 10:45:32.371 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-08-02 10:45:32.380 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-02 10:45:34.504 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-02 10:45:34.508 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-08-02 10:45:34.552 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 0 Elasticsearch repository interfaces.
2025-08-02 10:45:34.558 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-02 10:45:34.559 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-08-02 10:45:34.574 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-08-02 10:45:34.588 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-02 10:45:34.589 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-02 10:45:34.621 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 JPA repository interfaces.
2025-08-02 10:45:34.648 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-02 10:45:34.652 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-02 10:45:34.682 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-08-02 10:45:35.943 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-08-02 10:45:35.970 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-08-02 10:45:35.971 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-02 10:45:35.972 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-08-02 10:45:36.250 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-02 10:45:36.250 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3809 ms
2025-08-02 10:45:36.599 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-08-02 10:45:36.960 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-02 10:45:38.356 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-02 10:45:38.517 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-08-02 10:45:39.056 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-02 10:45:39.580 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-08-02 10:45:40.319 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-02 10:45:40.359 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-02 10:45:40.394 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 超级管理员功能已启用
2025-08-02 10:45:40.394 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户ID数量: 2
2025-08-02 10:45:40.398 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员登录账号数量: 2
2025-08-02 10:45:40.398 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户名数量: 2
2025-08-02 10:45:44.496 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-02 10:45:49.527 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-08-02 10:45:49.555 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-08-02 10:45:51.462 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 20.384 seconds (JVM running for 23.29)
2025-08-02 10:46:00.907 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-02 10:46:00.908 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-02 10:46:00.909 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-02 10:46:01.298 [http-nio-8285-exec-1] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /api/temp/users/upload, Method: POST, IP: 0:0:0:0:0:0:0:1
2025-08-02 10:46:03.608 [http-nio-8285-exec-1] INFO  c.d.p.t.u.service.impl.UserImportServiceImpl - 开始处理用户导入...
2025-08-02 10:46:08.398 [http-nio-8285-exec-1] INFO  c.d.p.t.u.service.impl.UserImportServiceImpl - 从Excel中解析出 11288 个用户
2025-08-02 10:46:08.401 [http-nio-8285-exec-1] INFO  c.d.p.t.u.service.impl.UserImportServiceImpl - 加载了 1478 个部门ID映射
2025-08-02 10:46:09.719 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱景超' 的部门ID '2126492483a54d79a84109dd24e944a8' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:10.785 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '崔亚威' 的部门ID 'ZB0300006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:12.684 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '左振元' 的部门ID 'ZB3600013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:13.223 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '黄龙飞' 的部门ID 'ZB0400002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:13.303 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '华德' 的部门ID 'ZB0400002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:14.078 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '丁肇星' 的部门ID 'ZR0200007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:14.365 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '史大宁' 的部门ID 'ZB0400002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:15.038 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张俊兴' 的部门ID 'ZB0300001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:16.927 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '邹海' 的部门ID 'X57140500' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:17.166 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐康' 的部门ID 'ZG0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:17.305 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘梦桐' 的部门ID 'ZU1600002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:17.849 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李明达' 的部门ID 'ZB0300006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:18.424 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李小亮' 的部门ID 'ZU1600001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:19.127 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周衍' 的部门ID 'ZR0200006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:19.424 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘红光' 的部门ID 'ZG0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:21.186 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '曹剑' 的部门ID 'ZB3700010' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:21.798 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈学军' 的部门ID 'X47100200' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:22.798 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '施海峰' 的部门ID 'ZG0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:22.945 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '龙云翔' 的部门ID 'ZK1400003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:23.023 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘传昆' 的部门ID 'ZL0002000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:23.503 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周纯康' 的部门ID 'ZB3400002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:23.805 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '高健鸣' 的部门ID 'ZB3600007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:24.824 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周婷' 的部门ID 'X63190013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:24.903 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张巧月' 的部门ID 'X63190013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:25.704 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郑志祥' 的部门ID 'ZF0700010' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:26.127 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨申辉' 的部门ID 'ZJ0000085' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:26.207 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '钟平' 的部门ID 'ZB3600013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:26.677 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱德川' 的部门ID 'ZU1600002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:27.726 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '冯玉甲' 的部门ID 'ZU1600002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:28.638 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '林柏汶' 的部门ID 'ZU1600002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:28.703 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李佳玉' 的部门ID 'ZN2900004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:29.584 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨健' 的部门ID 'ZN6100007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:30.838 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '何方' 的部门ID 'ZB0400002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:32.557 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '钱敏芳' 的部门ID 'X43120000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:33.344 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '夏福昇' 的部门ID 'ZB0200000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:34.587 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '闫浩' 的部门ID 'ZR0200007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:35.926 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张文超' 的部门ID 'ZB2700009' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:36.278 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郑江' 的部门ID 'ZB0400002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:40.824 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张微微' 的部门ID 'ZF0500015' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:41.663 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '盛子荣' 的部门ID 'X47100100' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:41.905 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王海波' 的部门ID 'ZK1400004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:42.463 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '史巧云' 的部门ID 'ZN2600003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:42.597 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '梅军' 的部门ID 'ZR0100004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:43.303 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王志国' 的部门ID 'X52060011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:44.103 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '卜康乐' 的部门ID 'ZA0900008' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:45.065 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '赵孝章' 的部门ID 'ZB3800003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:45.767 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张建华' 的部门ID 'X63190012' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:46.423 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐宁' 的部门ID 'c2f7b989434541dcafe2b34edea126e3' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:46.806 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '冯为' 的部门ID 'X34030000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:46.946 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '马瑞全' 的部门ID 'ZB3700006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:47.103 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘大勇' 的部门ID 'XC2000401' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:47.423 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '袁金甲' 的部门ID 'ZF0700019' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:47.744 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '梁堃' 的部门ID 'ZR0200006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:48.223 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '魏锋' 的部门ID 'ZG0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:49.285 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周周' 的部门ID 'ZR0200007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:49.945 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李志萍' 的部门ID 'ZR0200008' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:51.535 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '于彩文' 的部门ID 'ZB3400003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:51.664 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张胜宝' 的部门ID 'ZB0300005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:51.835 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈勇' 的部门ID 'X42050014' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:52.623 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '武蓉' 的部门ID 'ZR0200011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:52.784 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杜星' 的部门ID 'ZR0200007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:53.918 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '姜文晨' 的部门ID 'ZG0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:54.063 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘汉卿' 的部门ID 'ZR0200009' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:54.624 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杜轶' 的部门ID 'ZR0200009' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:55.324 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郑郁' 的部门ID '1917c82911634ee09fd59b2ab5ab3758' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:55.444 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '石向明' 的部门ID '1917c82911634ee09fd59b2ab5ab3758' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:56.743 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘洋' 的部门ID 'ZB1800001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:56.925 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郭帅' 的部门ID 'ZB3600002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:57.305 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '熊正毅' 的部门ID 'ZI0000004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:58.025 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '代红燕' 的部门ID 'ZF0500001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:58.084 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈伟' 的部门ID 'ZB2000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:58.463 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '龚洪卫' 的部门ID 'ZB4100005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:59.398 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '高永亮' 的部门ID 'ZR0200001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:46:59.486 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李梓齐' 的部门ID 'ZG0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:00.223 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '祁鹏飞' 的部门ID 'c3e83043149b47c9a046a2c0cfa70dfa' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:01.104 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '潘德军' 的部门ID 'ZG0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:01.463 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐椿森' 的部门ID 'ZF0500009' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:02.023 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '蔡铤' 的部门ID 'ZG0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:02.204 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '叶青' 的部门ID 'X02170002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:02.477 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王益宏' 的部门ID 'X02170002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:02.623 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周永路' 的部门ID 'ZB3400002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:02.703 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '林同越' 的部门ID 'XC2000500' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:03.717 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李旭' 的部门ID 'ZL0004000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:03.784 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王庆民' 的部门ID 'c2f7b989434541dcafe2b34edea126e3' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:04.235 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '汲德建' 的部门ID 'ZL0004000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:04.463 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '蒋艳' 的部门ID 'ZZ1000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:04.904 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '庞宗全' 的部门ID 'XC2000500' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:06.103 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张雅萍' 的部门ID 'ZI0000011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:06.404 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王志强' 的部门ID 'ZB3800003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:06.478 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐宁福' 的部门ID 'ZB0300008' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:06.844 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈荣煌' 的部门ID 'X74030001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:08.063 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '卢平' 的部门ID 'ZB3400005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:08.438 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '严海江' 的部门ID 'ZR0200000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:09.825 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '高飞' 的部门ID 'ZG0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:09.983 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张桂梅' 的部门ID 'ZG0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:13.144 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李浩然' 的部门ID 'ZZ0800036' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:15.063 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '嵇海东' 的部门ID 'X76020002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:15.143 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '许晓东' 的部门ID 'X76020002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:15.584 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '宋效力' 的部门ID 'X85030001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:15.663 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '沈嘉农' 的部门ID 'X34010002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:15.784 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '林如林' 的部门ID 'X63170001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:16.285 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈怒' 的部门ID 'c94e12d7ae5a415c9ff9ca515d7ab38d' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:16.366 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '程明' 的部门ID 'X34010002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:16.583 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '姜帆' 的部门ID '4f7e75fa317c482faf22ff48a762ef96' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:17.903 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李学军' 的部门ID 'X34040004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:18.063 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈国荣' 的部门ID 'X34040004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:21.983 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 'Filipi' 的部门ID 'ZB3600012' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:22.863 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '阮昊' 的部门ID 'ZU1600002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:24.143 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周有华' 的部门ID '5490e88de8d347789d557e08487d3352' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:24.223 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李明' 的部门ID 'X34070000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:24.446 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐敏' 的部门ID 'X76030002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:26.586 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '何刚' 的部门ID 'ZB3400002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:26.879 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周学保' 的部门ID 'f1cb750b414f47bfadec9ebbc9efeb5f' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:27.143 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '石昊' 的部门ID 'ZB3400009' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:28.103 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '印浩' 的部门ID '70bd03fd2244449e983de1100c38649d' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:32.224 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王建生' 的部门ID 'X63190011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:32.446 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张健' 的部门ID 'b719acaa280a46ad82dc16023a1aa115' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:32.518 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '洪伟宝' 的部门ID 'X66090001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:32.590 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '洪礼伟' 的部门ID 'X63180001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:32.783 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '谢树兴' 的部门ID '0f99680d9ae74e638c7482346173cc5f' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:32.863 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘广运' 的部门ID 'd9a332271eb04e49815974f87372c16f' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:33.078 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨晨' 的部门ID '56634efd9c724fcc8e101fd8dab83f23' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:37.312 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '龙良德' 的部门ID 'X63190013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:37.404 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '梁辉' 的部门ID '85d17c05085546aa9765f2114fdc2f32' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:37.691 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '石玉柱' 的部门ID '85d17c05085546aa9765f2114fdc2f32' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:37.764 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨坚' 的部门ID '85d17c05085546aa9765f2114fdc2f32' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:41.463 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '苏菲凡' 的部门ID 'ZF0500018' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:43.623 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '惠睿智' 的部门ID 'X02170002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:46.383 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '娄旭东' 的部门ID 'b719acaa280a46ad82dc16023a1aa115' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:46.543 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈铭' 的部门ID 'b719acaa280a46ad82dc16023a1aa115' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:46.703 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王有志' 的部门ID 'X74020005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:47.143 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱文俊' 的部门ID '85d17c05085546aa9765f2114fdc2f32' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:48.223 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '叶雨航' 的部门ID 'XC2000402' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:48.384 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王飞龙' 的部门ID 'XC2000200' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:50.204 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李祖锐' 的部门ID 'X06020000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:51.543 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李东红' 的部门ID 'ZU1500000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:51.703 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '夏福豪' 的部门ID 'XC2000400' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:53.398 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐海涛' 的部门ID 'X41000003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:53.477 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李明' 的部门ID 'X41000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:54.606 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '汤如宁' 的部门ID 'X85030001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:54.685 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张春明' 的部门ID 'ZB0300005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:55.323 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '赵崇辛' 的部门ID 'ZB0300005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:55.563 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周爱明' 的部门ID 'X85030001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:56.943 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘宁' 的部门ID 'ZB0300003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:57.445 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郭一凡' 的部门ID 'ZB0300006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:47:58.678 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王浩' 的部门ID '1917c82911634ee09fd59b2ab5ab3758' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:00.083 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '曹国俊' 的部门ID 'ZB3600007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:00.157 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王良伟' 的部门ID 'ZB4200005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:00.245 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '董成斌' 的部门ID 'ZG0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:01.063 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '赵莉' 的部门ID 'XC2000200' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:01.525 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '安茜垚' 的部门ID 'ZB3600012' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:01.797 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张正威' 的部门ID 'ZM0000044' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:02.123 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐玉英' 的部门ID 'ZB3600012' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:02.317 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '万炳松' 的部门ID 'ZB3400002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:02.463 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨波' 的部门ID 'ZB0300005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:02.903 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '苏睿' 的部门ID 'X85030001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:07.877 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李杨' 的部门ID 'ZB4200005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:08.317 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李莉' 的部门ID 'ZR0200003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:08.543 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '奚旭霞' 的部门ID 'ZF1600031' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:11.063 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '沈飞飞' 的部门ID 'X41000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:14.787 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '梁子昂' 的部门ID 'XC2000400' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:15.423 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '赵不愚' 的部门ID 'ZB2300004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:17.205 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '丁强' 的部门ID '2745219878e84ba4a1070e110292e6ac' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:18.326 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张凯' 的部门ID '43776ed897b34029ae494a82235f8cce' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:19.383 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈煜' 的部门ID 'ZK1400007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:19.663 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郑宁' 的部门ID 'X63240008' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:19.744 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李小玲' 的部门ID 'X74030001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:19.823 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周永正' 的部门ID 'X74030001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:20.044 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '曾涛' 的部门ID 'X63170003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:20.245 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '梁勇' 的部门ID '70bd03fd2244449e983de1100c38649d' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:21.183 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '倪浜' 的部门ID 'c3e83043149b47c9a046a2c0cfa70dfa' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:21.464 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郭琳' 的部门ID '23c758a687584c0ead0aecb84004db1b' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:24.183 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '包文波' 的部门ID 'ZI0000004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:24.365 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '黄佳伟' 的部门ID 'ZB3700007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:24.448 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '汪长清' 的部门ID 'ZM0000073' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:24.685 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '何伟建' 的部门ID 'ZB4000006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:25.062 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王琰然' 的部门ID 'ZL0004000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:25.143 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王红' 的部门ID 'ZB1700001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:25.383 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '施昊' 的部门ID 'ZL0002000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:25.526 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '耿学玉' 的部门ID 'ZL0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:25.606 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '毛敏' 的部门ID 'ZL0004000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:25.685 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈金勇' 的部门ID 'ZL0002000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:25.844 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张秀和' 的部门ID 'ZL0003000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:25.943 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王炜' 的部门ID 'ZL0001000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:26.023 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '施培' 的部门ID 'ZL0001000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:26.103 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈赤' 的部门ID 'ZL0001000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:26.184 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王静' 的部门ID 'ZL0004000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:26.243 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '柴惠贤' 的部门ID 'ZL0000009' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:26.326 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈红' 的部门ID 'ZL0004000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:26.397 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨春' 的部门ID 'ZM0000073' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:26.478 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '鞠晶' 的部门ID 'ZL0004000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:26.646 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王德垠' 的部门ID 'ZB1600000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:26.717 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王诗旭' 的部门ID 'ZJ0000075' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:26.783 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '孙通' 的部门ID 'ZJ0000077' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:26.843 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '唐菲' 的部门ID 'ZJ0000075' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:26.903 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李井先' 的部门ID 'ZJ0000071' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:26.983 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '庄宁心' 的部门ID 'ZJ0000081' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:27.077 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘文龙' 的部门ID 'ZJ0000071' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:27.158 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱鹏举' 的部门ID 'ZJ0000073' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:27.263 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '田云' 的部门ID 'ZJ0000088' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:27.351 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱庆祺' 的部门ID 'ZJ0000073' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:27.432 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '米传汇智' 的部门ID 'ZJ0000077' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:27.503 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张洋' 的部门ID 'ZJ0000088' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:27.583 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '花蕾' 的部门ID 'ZJ0000075' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:27.663 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘丽' 的部门ID 'ZJ0000087' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:27.743 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张梦婷' 的部门ID 'ZJ0000091' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:29.166 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张国忠' 的部门ID '1917c82911634ee09fd59b2ab5ab3758' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:33.224 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '姜继洪' 的部门ID 'X31190000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:33.302 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '颜莉' 的部门ID 'ZF0500006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:33.476 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '余震' 的部门ID 'ZF0700012' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:33.883 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李云龙' 的部门ID 'ZL0003000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:33.957 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王靖宇' 的部门ID 'ZL0002000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:34.044 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '鞠鹏' 的部门ID 'ZL0004000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:34.263 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张奇山' 的部门ID 'ZL0003000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:34.325 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '殷俊' 的部门ID 'ZL0004000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:34.403 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '阮建勇' 的部门ID 'ZL0005000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:34.463 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '高海燕' 的部门ID 'ZL0001000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:34.523 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '严斌' 的部门ID 'ZL0003000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:34.598 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '屠钧' 的部门ID 'ZL0001000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:34.743 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王明明' 的部门ID 'ZL0000009' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:34.824 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王淳' 的部门ID 'ZL0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:34.903 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '尹桂军' 的部门ID 'ZL0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:34.982 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '彭士钟' 的部门ID 'ZL0004000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:35.062 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨威' 的部门ID 'ZB3300005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:35.143 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周丽' 的部门ID 'ZB3300006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:35.327 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '万俊英' 的部门ID 'ZL0004000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:35.422 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '胡楠' 的部门ID 'ZB3300010' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:35.503 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '姚继东' 的部门ID 'ZB3300007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:35.644 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王宇' 的部门ID 'ZL0000009' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:35.718 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张汉奇' 的部门ID 'ZL0000009' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:35.798 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周国清' 的部门ID 'ZL0002000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:36.023 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '闵胜' 的部门ID 'ZB3600013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:36.103 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '范明昊' 的部门ID 'ZI0100001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:36.182 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李美玲' 的部门ID 'ZL0001000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:36.342 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '牛振清' 的部门ID 'ZB1000025' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:36.423 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王豪杰' 的部门ID 'ZJ0100032' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:36.517 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '姚金陵' 的部门ID 'ZJ0100032' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:39.319 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '常孝斌' 的部门ID 'X85030001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:43.823 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐晓春' 的部门ID 'ZB3400003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:43.982 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '顾垒' 的部门ID 'ZI0000013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:44.223 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '万华' 的部门ID 'ZB3600001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:44.563 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '董彤彤' 的部门ID 'X06020000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:44.637 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张帅' 的部门ID 'XC2000200' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:44.717 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '林国强' 的部门ID 'ZB3800001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:45.244 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李永锋' 的部门ID 'ZF0700011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:45.398 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨思明' 的部门ID 'ZF2100009' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:45.718 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘斌' 的部门ID 'ZA0400000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:47.558 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '诸晓邱' 的部门ID 'X34070000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:47.624 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '许可' 的部门ID 'X34070001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:47.703 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈闻新' 的部门ID 'X76020001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:47.943 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '赵志杰' 的部门ID 'ZA0900014' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:48.024 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '曹宁' 的部门ID 'X76010000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:48.103 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘明海' 的部门ID 'X76030002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:48.182 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈宁文' 的部门ID 'X76030002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:48.325 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '程菲' 的部门ID 'ZF0500001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:49.556 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘锦春' 的部门ID 'ZB3700010' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:50.822 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '秦宇路' 的部门ID 'ZF0500011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:51.282 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨辉' 的部门ID 'ZF0500005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:51.423 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '胡卫峰' 的部门ID 'ZG0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:52.364 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈成' 的部门ID 'ZB0300007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:52.663 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '董波' 的部门ID '32a0a2d0eeab4442abd6b5f108a3bc73' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:52.917 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '方辉武' 的部门ID 'ZF0700022' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:52.997 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '楚觉非' 的部门ID 'ZB3700001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:53.076 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '吴顺端' 的部门ID 'ZB0300008' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:53.543 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '邱诗迪' 的部门ID 'XC2000400' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:54.424 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '宾凤' 的部门ID 'ZF1300001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:54.623 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '娄大刚' 的部门ID 'X42060008' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:55.103 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '柳传刚' 的部门ID 'ZR0200009' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:55.244 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈振贵' 的部门ID 'c2f7b989434541dcafe2b34edea126e3' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:55.624 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张时轶' 的部门ID 'ZR0200011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:55.783 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张伟' 的部门ID 'X63240007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:56.877 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '黄斌' 的部门ID 'ZK1400003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:48:57.023 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '吕永祥' 的部门ID '43776ed897b34029ae494a82235f8cce' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:00.143 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '黄涛' 的部门ID 'ZF0500005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:00.822 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '包学旻' 的部门ID 'ZB3600012' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:01.783 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '夏永康' 的部门ID 'ZZ1000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:02.383 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王玮' 的部门ID 'X34040006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:02.543 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '夏国庆' 的部门ID 'X42060009' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:03.028 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '袁啟明' 的部门ID 'X42060007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:03.316 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨启友' 的部门ID 'X42060013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:04.204 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '吴振祥' 的部门ID 'X85030001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:04.277 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '屠加翠' 的部门ID 'X34010002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:04.343 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张国忠' 的部门ID 'X34040006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:10.046 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '于宁波' 的部门ID '9d7839db15ef4253b2b9ce6bd6965b0a' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:11.022 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘传宝' 的部门ID 'X42060017' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:12.766 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王新城' 的部门ID 'b719acaa280a46ad82dc16023a1aa115' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:12.837 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '惠自钢' 的部门ID 'b719acaa280a46ad82dc16023a1aa115' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:12.917 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '黄沛' 的部门ID '8f45f98e1dd746b5b81bc0229d905ac3' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:13.345 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈新军' 的部门ID 'ZB0300008' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:13.663 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '沈传明' 的部门ID '85d17c05085546aa9765f2114fdc2f32' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:16.183 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '任园' 的部门ID '83c6cf9a86244c18a4707b178d32ba55' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:16.502 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱秀梅' 的部门ID 'ZL0004000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:16.583 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '俞亮亮' 的部门ID 'ZB3400001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:17.238 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐春松' 的部门ID 'ZB3400001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:18.742 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐焱' 的部门ID 'ZI0000020' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:19.523 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王勇' 的部门ID '9f84bdb6905c4262babffabc6a04971d' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:19.902 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王鹤洋' 的部门ID '83c6cf9a86244c18a4707b178d32ba55' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:20.103 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '程雪琦' 的部门ID 'ZB2300004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:20.183 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '焦刚' 的部门ID 'ZI0000004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:21.582 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '吴刚' 的部门ID 'X54100000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:26.243 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '金思超' 的部门ID 'ZB3600012' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:26.397 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王子清' 的部门ID 'ZB0300004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:26.543 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '马超' 的部门ID 'ZB0300004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:26.623 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱晶' 的部门ID 'ZB0300004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:26.758 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '姬文斌' 的部门ID 'ZB3400001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:27.063 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王玮' 的部门ID 'ZB3700008' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:27.283 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '孙国良' 的部门ID 'ZB2500001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:27.686 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '姚利军' 的部门ID 'ZB3700008' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:28.182 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '阮玥' 的部门ID 'X42060007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:28.806 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱槐' 的部门ID 'X48020001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:29.702 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王徐杨' 的部门ID 'ZI0000010' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:35.238 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈瑞阳' 的部门ID 'ZJ1100011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:35.364 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈明' 的部门ID 'ZB2500001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:35.564 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '苏翊华' 的部门ID 'ZB3600006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:35.636 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈煜华' 的部门ID 'ZB3600006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:35.720 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王祥' 的部门ID 'ZB3400013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:36.022 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周静思' 的部门ID 'XB5000100' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:36.082 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郑有志' 的部门ID 'ZZ0800066' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:36.584 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '钱荣' 的部门ID 'ZN4300006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:36.963 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '于露' 的部门ID 'ZZ0200002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:37.197 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郭德春' 的部门ID 'X42060011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:37.263 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '钱凯' 的部门ID 'ZL0002000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:38.663 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '汪斌' 的部门ID 'X76020002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:38.743 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '鲍立新' 的部门ID 'X34030000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:41.982 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '凌建忠' 的部门ID '3b88b0747961446b8b9dbbe45dd258a8' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:42.464 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李海' 的部门ID 'X76020002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:42.543 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '顾永春' 的部门ID 'X85030001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:42.623 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '卞向荣' 的部门ID 'X85030001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:42.703 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '孙嵘' 的部门ID 'X85030001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:42.784 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '唐振宇' 的部门ID 'X85030001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:42.842 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '胡蓉' 的部门ID 'X85030001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:42.902 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '胡金山' 的部门ID 'X63170002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:43.122 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '马靖' 的部门ID 'X48020001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:43.323 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李登桂' 的部门ID 'c2f7b989434541dcafe2b34edea126e3' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:43.683 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '吴良霄' 的部门ID '4f7e75fa317c482faf22ff48a762ef96' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:44.023 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '许研' 的部门ID 'ZB3400006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:44.197 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '胡靓' 的部门ID '0609aefed92d4a789478d906e64244ad' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:44.903 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '毕玉轩' 的部门ID 'ZB3600006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:45.063 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈娴' 的部门ID 'b719acaa280a46ad82dc16023a1aa115' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:46.045 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '谢鑫' 的部门ID 'ZL0000009' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:46.126 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '孙俊' 的部门ID 'ZA0400000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:46.691 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王慧芳' 的部门ID 'ZL0002000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:47.184 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '董昌磊' 的部门ID 'X42050005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:52.783 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱维' 的部门ID 'ZB3700007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:52.863 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '谢海涛' 的部门ID 'ZL0000009' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:52.942 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '黄卫东' 的部门ID 'ZZ1300001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:53.502 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '季正伟' 的部门ID 'ZB3400001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:53.623 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '姚静雅' 的部门ID 'ZI0000014' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:53.704 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '谢惠婷' 的部门ID 'ZN2900004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:53.783 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '孙兴稼' 的部门ID 'ZN6100008' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:53.863 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '童翔茹' 的部门ID 'ZL0004000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:55.582 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郭靖' 的部门ID 'ZM2300017' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:56.142 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '简宇星' 的部门ID '83c6cf9a86244c18a4707b178d32ba55' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:56.204 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘珂含' 的部门ID 'ZI0000011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:57.197 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '易飞' 的部门ID 'ZI0100040' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:58.283 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周立年' 的部门ID 'X74020005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:49:58.503 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '潘斌' 的部门ID '85d17c05085546aa9765f2114fdc2f32' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:00.597 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '熊林' 的部门ID 'X63190013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:02.924 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '汪海溧' 的部门ID 'ZB2500001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:02.997 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李云飞' 的部门ID 'ZB0300004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:03.065 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张蒙蒙' 的部门ID 'ZB3400005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:03.942 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '谷庆斌' 的部门ID 'ZB1800001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:04.862 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘源' 的部门ID 'X42060016' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:05.437 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王寅' 的部门ID 'ZF2000011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:05.943 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘兵' 的部门ID 'ZI0000004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:06.010 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '吴忠明' 的部门ID 'ZI0000004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:08.524 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '阮群峰' 的部门ID 'ZZ0700003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:10.503 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '翟军' 的部门ID 'X43100000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:12.263 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '曹建军' 的部门ID 'ZK1400021' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:12.503 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈维义' 的部门ID 'ZK0100013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:12.583 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈雷' 的部门ID 'ZK0100042' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:13.383 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱春茂' 的部门ID 'ZB2700009' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:14.143 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱成杰' 的部门ID '32a0a2d0eeab4442abd6b5f108a3bc73' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:17.402 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周俊' 的部门ID 'X34070001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:17.463 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '时晓峰' 的部门ID 'X34070001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:17.543 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '唐建强' 的部门ID 'X34010002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:17.876 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '许春虎' 的部门ID 'X34010002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:17.944 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '施晓斌' 的部门ID 'X63170002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:18.443 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '赵瀚赢' 的部门ID 'X34040006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:18.644 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '孔繁强' 的部门ID 'ZB0400002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:19.382 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王惠云' 的部门ID 'ZF1600003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:20.062 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郑荣' 的部门ID 'X34040005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:22.342 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '方龙海' 的部门ID 'ZN0100014' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:23.542 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '沈峰' 的部门ID 'ZB3600016' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:23.622 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '马纪强' 的部门ID 'ZB4200005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:28.303 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱龙胜' 的部门ID 'X76020001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:28.365 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '孙祥' 的部门ID 'X34040001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:28.444 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郑庆' 的部门ID 'ZA0900007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:28.518 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周建联' 的部门ID 'X76030002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:28.812 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张健' 的部门ID 'X34040006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:28.883 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '程亮' 的部门ID 'X48020001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:28.957 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周家坤' 的部门ID 'c2f7b989434541dcafe2b34edea126e3' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:29.023 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '梁杰' 的部门ID '43776ed897b34029ae494a82235f8cce' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:29.262 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱继' 的部门ID 'ZB3800002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:32.284 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨铁卫' 的部门ID 'ZG0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:32.982 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周庆东' 的部门ID 'ZI0200020' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:33.484 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李玮' 的部门ID 'ZI0200020' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:34.103 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王利斌' 的部门ID 'ZB3700010' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:34.182 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '章楚澜' 的部门ID 'ZK1600016' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:34.324 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '葛飞' 的部门ID 'ZZ0100004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:34.463 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王玉莹' 的部门ID 'ZZ0100005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:34.703 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王凯峰' 的部门ID 'X63190011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:34.917 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱立荣' 的部门ID 'X63190012' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:35.210 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '曹杨东' 的部门ID 'ZB4000005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:36.022 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '竺德平' 的部门ID 'X63190012' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:36.163 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '姚磊' 的部门ID 'X63190013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:36.882 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '魏松' 的部门ID 'X63180002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:36.958 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '马治国' 的部门ID 'X63180005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:40.583 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '邢俊霞' 的部门ID 'ZB3700007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:41.182 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '赵浚哲' 的部门ID 'ZB3400002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:44.665 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李文萍' 的部门ID 'XC2000500' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:44.982 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '薛彬' 的部门ID 'ZN2600008' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:45.042 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '谷全宝' 的部门ID 'X46020302' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:47.726 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王宝园' 的部门ID 'ZG0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:48.291 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '胡晓峰' 的部门ID 'b5802baf40694dea964fd4efae8beed5' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:49.862 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王春江' 的部门ID 'X48020001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:50:58.917 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '孙运福' 的部门ID 'ZJ0100032' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:00.637 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '钱志胜' 的部门ID '4f7e75fa317c482faf22ff48a762ef96' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:03.276 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '韩功赟' 的部门ID 'X54010000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:03.824 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '伍昊' 的部门ID '508a2dbcc28845a59e842eeb1f6a8e7a' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:04.462 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨虎宝' 的部门ID 'ZL0004000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:04.542 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈益生' 的部门ID 'ZL0002000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:04.965 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈克利' 的部门ID 'XC2000500' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:05.036 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '蒋桂民' 的部门ID 'X63240004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:05.103 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王庆' 的部门ID 'ZA0400000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:06.822 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张雪' 的部门ID 'ZR0200003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:07.142 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '吴正虎' 的部门ID 'ZR0200002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:08.405 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王瀚林' 的部门ID 'ZR0200002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:10.023 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '卞娴' 的部门ID 'ZZ1000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:10.477 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郝燕' 的部门ID 'XC2000200' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:12.182 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李阴忠' 的部门ID 'X34040004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:12.262 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘长远' 的部门ID 'X34060000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:14.476 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '戴卫昌' 的部门ID 'ZA0400000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:15.443 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王淑亮' 的部门ID 'X63180001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:17.742 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '欧陈' 的部门ID 'X76020002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:17.822 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐俐俐' 的部门ID 'X76010000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:17.902 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '孙嘉伟' 的部门ID 'ZZ1000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:18.303 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '章大庆' 的部门ID 'ZB3800001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:19.405 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '曹宏娣' 的部门ID 'XC2000500' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:19.729 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '赵李斌' 的部门ID 'X34030000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:20.164 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '尹雨晨' 的部门ID 'X63190011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:21.116 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '黄凯迪' 的部门ID 'ZF0500006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:21.284 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '柏玲' 的部门ID 'X63190011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:21.543 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '林从洋' 的部门ID 'X63190011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:22.543 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张惠君' 的部门ID 'ZZ0100004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:23.422 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '魏宏彬' 的部门ID 'ZI0500016' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:23.637 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张正权' 的部门ID 'X63170004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:24.062 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '成晨' 的部门ID 'bea498ab2afc46a9ae2460db0272d2d1' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:24.142 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '卞鹏浩' 的部门ID 'X46020302' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:24.343 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王玉琴' 的部门ID 'ZF0500002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:25.862 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '易金兴' 的部门ID 'ZB3600015' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:26.182 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王海朋' 的部门ID 'X63190011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:26.397 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '韩露' 的部门ID 'X63190012' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:27.357 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杜积明' 的部门ID 'XC2000500' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:27.423 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '冯玥' 的部门ID 'XC2000500' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:27.677 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李豪' 的部门ID 'X66080000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:27.843 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '闫晓军' 的部门ID 'f1cb750b414f47bfadec9ebbc9efeb5f' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:27.920 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李帅' 的部门ID 'X54010000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:33.877 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '易勇' 的部门ID 'ZB3400006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:37.142 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '林贵明' 的部门ID 'ZB3800003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:38.805 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '赵卫明' 的部门ID 'X41000400' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:39.224 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王红幼' 的部门ID 'ZF1700003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:41.022 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '胡敬兵' 的部门ID 'af66b82e9bf541cbb646d39b25c99096' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:41.603 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李龙' 的部门ID 'ZB3700010' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:42.022 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘晓' 的部门ID 'ZY0100000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:45.183 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张亦宸' 的部门ID 'c3e83043149b47c9a046a2c0cfa70dfa' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:46.117 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '余猛' 的部门ID 'X74020005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:46.197 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '胡国威' 的部门ID 'X74030001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:46.742 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '钱惠婷' 的部门ID 'X63030000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:48.160 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '余亮' 的部门ID 'X63170003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:49.476 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '胡湘漪' 的部门ID 'X63190011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:49.923 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '雍乐' 的部门ID 'X85030001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:51.332 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐益平' 的部门ID 'X76020002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:51.902 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '殷文彪' 的部门ID 'ZB3800001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:52.862 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王剑' 的部门ID 'cdf37e5911f3417baec8a3acdabfbbf7' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:53.382 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王祥辉' 的部门ID 'X63190011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:53.462 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '姜虎' 的部门ID 'X63190013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:54.850 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王宇竹' 的部门ID '1917c82911634ee09fd59b2ab5ab3758' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:56.102 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周嬉' 的部门ID 'ZR0200003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:57.245 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '何俊' 的部门ID 'X63180004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:51:58.262 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '崔毅' 的部门ID 'ZB3600002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:04.200 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨桥东' 的部门ID 'XC2000300' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:06.147 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杜晃' 的部门ID 'ZB3400004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:06.663 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '田田' 的部门ID 'XC2000500' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:06.742 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王家宝' 的部门ID 'b719acaa280a46ad82dc16023a1aa115' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:07.442 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王欣晨' 的部门ID 'X47100800' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:12.222 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '任浣晨' 的部门ID 'X63190011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:12.862 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郑健' 的部门ID 'X63190011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:13.599 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '高春果' 的部门ID 'X63170002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:15.222 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '吕世龙' 的部门ID 'X42050010' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:15.301 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈德胜' 的部门ID 'ZG0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:16.902 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '韩朗' 的部门ID 'X46020302' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:18.757 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈宇' 的部门ID 'X63190011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:18.825 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈晓霞' 的部门ID 'ZF0800002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:20.622 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '颜连浦' 的部门ID 'X63180002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:22.182 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '沙鹏飞' 的部门ID 'ZK1400011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:22.343 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '赵宇驰' 的部门ID 'XC2000401' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:26.131 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '唐益' 的部门ID 'db1ce753b6e64f26809c3c51b9c7561f' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:26.423 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李福存' 的部门ID 'ZJ1100001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:26.524 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '常兴' 的部门ID 'ZJ0000081' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:26.850 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '黄立生' 的部门ID 'ZA0400000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:28.822 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 'WILLECKE RALF' 的部门ID 'ZK0100017' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:29.144 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '牟明' 的部门ID 'ZF1000003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:29.862 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '端震' 的部门ID 'b719acaa280a46ad82dc16023a1aa115' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:30.102 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '马青' 的部门ID 'b719acaa280a46ad82dc16023a1aa115' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:31.543 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '洪超' 的部门ID 'X41000100' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:31.942 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '孙敬明' 的部门ID '4f7e75fa317c482faf22ff48a762ef96' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:32.245 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '钱晓铭' 的部门ID 'X63210001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:32.630 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '袁瑾' 的部门ID 'ZK1400007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:34.322 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郑国文' 的部门ID 'X46020102' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:35.743 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '钱敏芬' 的部门ID 'X57120402' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:36.515 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李天文' 的部门ID 'XC2000500' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:36.925 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张硕' 的部门ID '32a0a2d0eeab4442abd6b5f108a3bc73' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:38.702 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '蔡依萍' 的部门ID 'ZB3600012' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:39.822 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李焚钢' 的部门ID 'X32290000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:40.836 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '董伟' 的部门ID 'X63030000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:40.923 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '韩志刚' 的部门ID 'ZB3800003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:40.986 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '汤佳鑫' 的部门ID 'X63190011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:41.862 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '韦晨鹏' 的部门ID '43776ed897b34029ae494a82235f8cce' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:43.021 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '夏燕' 的部门ID 'af66b82e9bf541cbb646d39b25c99096' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:44.915 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周玉洁' 的部门ID 'ZZ1000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:45.042 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '凌叶金' 的部门ID 'ZB4200002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:49.742 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '赵宏林' 的部门ID 'ZU1600002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:50.501 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨静' 的部门ID 'ZZ1000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:50.742 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张燕' 的部门ID 'ZZ1000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:50.844 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郭萍' 的部门ID 'f4bbadc9c0e6408fb9fc9f161fd8b704' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:51.303 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈明' 的部门ID 'X76020002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:51.450 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '叶加平' 的部门ID 'X76020001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:54.842 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '顾洪' 的部门ID 'X63180006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:56.402 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张爱文' 的部门ID 'X76020001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:56.476 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '顾晗之' 的部门ID 'X76020001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:57.421 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱腾启' 的部门ID 'X34040006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:52:59.602 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘剑萍' 的部门ID 'X63240002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:01.355 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '耿瑞金' 的部门ID 'ZM0100001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:01.422 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '江浩毓' 的部门ID 'ZF1600003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:01.564 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '孟桂荣' 的部门ID 'ZF1600002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:02.901 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '柏林' 的部门ID 'X63190012' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:03.676 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李兴军' 的部门ID 'X63190011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:03.982 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '孟子敏' 的部门ID 'X28080000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:04.742 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '霍进忠' 的部门ID 'ZB3400002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:05.883 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张洋' 的部门ID 'ZG0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:06.523 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '谢静' 的部门ID 'X63010000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:07.142 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '涂志伟' 的部门ID 'X34040004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:08.430 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张起' 的部门ID 'X41000100' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:08.582 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '戴巍' 的部门ID 'X41000100' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:09.062 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '曹再云' 的部门ID 'X63190013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:09.862 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郑志峰' 的部门ID '0609aefed92d4a789478d906e64244ad' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:09.942 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '葛传金' 的部门ID 'X41000100' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:13.597 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郦煦阳' 的部门ID 'ZB3400009' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:13.822 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王晓磊' 的部门ID 'ZB3600002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:15.302 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '康庆荣' 的部门ID 'X46020303' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:15.902 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈金鑫' 的部门ID '3a1804275c55449fac576f9e3e13d6c7' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:16.062 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '唐庆新' 的部门ID 'X46020202' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:16.365 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '肖开颜' 的部门ID 'ZN4300001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:16.444 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱大荣' 的部门ID 'X41000200' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:17.941 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张萍' 的部门ID 'ZF1600031' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:18.156 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '邢明虎' 的部门ID 'X63190013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:19.183 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张喜鸽' 的部门ID 'ZK0100038' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:21.522 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '倪小伟' 的部门ID 'X63190013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:22.102 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘海' 的部门ID 'X63170004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:24.763 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '赵云' 的部门ID 'X41000400' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:24.835 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '孙红涛' 的部门ID 'X41000002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:25.781 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张德健' 的部门ID 'ZG0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:26.222 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨努' 的部门ID 'ZN0100025' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:27.182 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘梦' 的部门ID 'ZJ0000097' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:29.461 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '赵航' 的部门ID 'ZG0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:29.678 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张春阳' 的部门ID 'ZK1400010' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:29.835 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨辉' 的部门ID 'ZB3600002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:32.142 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '殷皓' 的部门ID 'ZB3800003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:32.283 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李震' 的部门ID '29f60c9ed09f4971a325e9ec02b75269' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:33.662 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '严有宾' 的部门ID 'X34040005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:33.810 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘斌' 的部门ID 'X34040002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:35.342 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘礼华' 的部门ID 'ZK0100018' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:37.041 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '黄祺' 的部门ID 'ZJ0000060' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:37.277 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '葛沙沙' 的部门ID 'ZK1400005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:37.342 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王海' 的部门ID 'ZI0000008' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:37.436 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王一鸣' 的部门ID 'ZI0000005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:37.502 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈坤' 的部门ID 'ZF0700014' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:37.582 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '钱坤' 的部门ID 'ZI0000004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:37.661 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '曹庆' 的部门ID 'ZI0200036' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:37.742 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '任兆军' 的部门ID 'ZI0200020' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:37.901 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王天祺' 的部门ID 'ZI0000008' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:37.982 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈国泽' 的部门ID 'ZI1100002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:38.069 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李阳' 的部门ID 'ZI0200035' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:38.142 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李欣' 的部门ID 'ZK0100043' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:38.201 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '赵秋治' 的部门ID 'ZI0200034' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:38.501 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周浩' 的部门ID 'ZI0200014' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:38.661 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李浩' 的部门ID 'ZI0200034' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:38.755 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张黎敏' 的部门ID 'ZI0000016' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:38.822 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王佳丽' 的部门ID 'ZI0000006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:38.902 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘文忠' 的部门ID 'ZI0000012' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:38.981 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘露' 的部门ID 'X74010000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:39.302 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '高江辉' 的部门ID 'XC2000300' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:40.382 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '赵保其' 的部门ID 'ZF1800003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:40.942 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '蒋安宁' 的部门ID 'X63240003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:41.103 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '金菊' 的部门ID 'X74030001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:41.182 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '马昌亮' 的部门ID 'ZJ0100032' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:42.221 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '沈飞龙' 的部门ID 'X41000400' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:44.422 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '马军' 的部门ID 'X41000400' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:45.437 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '林天舒' 的部门ID 'ZF1800001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:45.883 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '吴诚' 的部门ID 'X85030001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:46.682 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐伟卓' 的部门ID 'ZF0600010' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:47.622 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱相锴' 的部门ID 'ZK1400006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:47.845 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '凌继中' 的部门ID 'ZB3300007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:48.042 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王和京' 的部门ID 'ZF0900000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:48.277 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱春林' 的部门ID 'X76030002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:50.581 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '吕玉玉' 的部门ID 'XB5000100' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:50.642 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '许志远' 的部门ID 'ZJ0000102' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:50.716 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '曹语茜' 的部门ID 'ZJ0000092' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:51.716 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '吴刚' 的部门ID 'ZB3400002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:55.302 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '秦玉春' 的部门ID 'ZK0100045' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:55.382 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘苏潇龙' 的部门ID 'X08040000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:55.516 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '桂俊' 的部门ID 'ZB1000011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:55.643 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '曹腾' 的部门ID 'ZB1000004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:55.922 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李可菲' 的部门ID 'ZF0500001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:56.061 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '金星宇' 的部门ID 'ZF0500011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:56.122 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '谢翔' 的部门ID 'ZB1000022' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:56.196 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '高杰' 的部门ID 'ZJ0100032' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:56.341 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '胡东' 的部门ID 'ZB1000005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:56.402 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周伟' 的部门ID 'ZB3300004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:56.462 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '车明珍' 的部门ID 'ZB3300005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:56.542 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '沙莎' 的部门ID 'ZB3300001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:56.685 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '祝永宗' 的部门ID 'ZJ0000063' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:57.562 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郑秋树' 的部门ID 'XC2000500' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:57.643 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '林峰' 的部门ID 'ZB3600007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:59.396 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '田峻峰' 的部门ID 'X85030001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:53:59.821 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈平昌' 的部门ID 'X63190013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:00.902 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王学兵' 的部门ID 'X63190012' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:01.130 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '黄之曦' 的部门ID 'ZI0000013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:01.203 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李伏强' 的部门ID 'ZI0100040' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:01.276 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐燕' 的部门ID 'ZI0100040' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:01.343 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '蒋勇' 的部门ID 'ZI0000014' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:01.421 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '胡钰平' 的部门ID 'ZI0000002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:01.581 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '裴植' 的部门ID 'ZI0000011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:01.661 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '沈王婷' 的部门ID 'ZI0000011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:01.742 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱家阳' 的部门ID 'ZI0000013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:01.822 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '邹道金' 的部门ID 'ZI0100040' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:01.982 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '马云峰' 的部门ID 'ZI0000024' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:02.042 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '舒宏涛' 的部门ID 'ZI0000005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:02.115 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '孙增强' 的部门ID 'ZI0100002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:02.251 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李钟玮' 的部门ID 'ZI0100040' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:02.322 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '戚阳阳' 的部门ID 'ZI0500004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:02.397 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '黄路飞' 的部门ID 'ZI0500004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:02.477 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郭俊屹' 的部门ID 'ZI0500004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:02.559 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王春芳' 的部门ID 'ZI0000004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:02.701 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '沈杨' 的部门ID 'ZI0000002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:02.783 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '蒋敏' 的部门ID 'ZI0500003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:02.862 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '黄丽燕' 的部门ID 'ZI0000011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:02.956 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '万齐才' 的部门ID 'ZI0500010' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:03.102 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '方明洁' 的部门ID 'ZI0000011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:03.182 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '翁金龙' 的部门ID 'ZI0000057' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:03.715 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '达庆辉' 的部门ID 'ZM0100023' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:03.796 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '纪超' 的部门ID 'ZF1300003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:06.650 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘宁' 的部门ID 'f1cb750b414f47bfadec9ebbc9efeb5f' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:09.501 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '祁海燕' 的部门ID 'ZZ1000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:11.836 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨宇轴' 的部门ID 'ZZ0800008' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:12.022 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '孙朕' 的部门ID 'X76020002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:18.516 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '余强' 的部门ID '93554127f2c7440884b4c58f595e8bd0' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:20.837 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李春善' 的部门ID 'ZZ0800066' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:21.102 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱秀林' 的部门ID 'X63190013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:22.066 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '汪滨' 的部门ID 'ZI0200008' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:22.141 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '黄姚鸣' 的部门ID 'ZB3600013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:22.221 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱德春' 的部门ID 'ZF0700010' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:22.301 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王晖' 的部门ID 'ZB1000006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:22.382 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徒宁刚' 的部门ID 'ZB1000006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:22.461 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陶立春' 的部门ID 'ZL0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:23.245 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '赵志芹' 的部门ID 'ZJ0000063' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:24.676 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王良根' 的部门ID 'ZB3800003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:24.742 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王顺友' 的部门ID 'ZA0400000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:26.163 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨杰' 的部门ID 'ZB3400002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:28.703 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王德' 的部门ID 'X63190011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:28.982 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '丁佳' 的部门ID 'ZN2600001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:29.061 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张荻飞' 的部门ID 'ZI0000003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:29.202 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周建南' 的部门ID 'ZF0600010' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:29.285 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李强' 的部门ID 'ZF0600010' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:29.364 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '顾平' 的部门ID 'ZI0000004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:29.444 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李纪磊' 的部门ID 'ZF0700000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:29.821 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '蔡洋' 的部门ID 'ZB1000019' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:29.981 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '江汇洋' 的部门ID 'ZB1700015' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:30.061 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '尹萍' 的部门ID 'ZB1700007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:30.141 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨远明' 的部门ID 'ZB3600006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:31.821 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杜道强' 的部门ID 'X34040002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:31.960 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐志江' 的部门ID 'X63190012' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:32.021 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王永强' 的部门ID 'X63190011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:33.522 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '于燕' 的部门ID 'XB5000200' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:34.182 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '薛坤' 的部门ID 'bea498ab2afc46a9ae2460db0272d2d1' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:34.502 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈康' 的部门ID '3a1804275c55449fac576f9e3e13d6c7' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:34.758 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王斌' 的部门ID 'ZJ0000095' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:35.836 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '沈朝彪' 的部门ID 'XC2000400' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:35.902 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '林思思' 的部门ID 'ZL0004000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:35.962 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '姚国亮' 的部门ID 'ZI0500004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:36.101 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐美艳' 的部门ID 'ZB3300004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:36.243 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '凌红风' 的部门ID 'ZF1000003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:36.395 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐乐怡' 的部门ID 'ZZ0100002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:36.549 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '梅瑞' 的部门ID 'ZJ0000073' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:36.622 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈祥争' 的部门ID 'ZZ0800066' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:36.682 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李倩' 的部门ID 'ZB3400005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:36.822 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '戚金芳' 的部门ID 'ZZ0400003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:39.501 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '狄金城' 的部门ID 'XC2000000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:40.163 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杜振喜' 的部门ID 'X85030001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:40.461 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈正新' 的部门ID 'X34070000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:40.781 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '袁宁' 的部门ID 'c2f7b989434541dcafe2b34edea126e3' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:42.302 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '夏世明' 的部门ID '93edec19c1db40c1b7e5707945f1de4b' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:43.381 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王勇' 的部门ID 'X63190013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:43.604 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张昕' 的部门ID 'ZJ0000098' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:43.676 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郝柏' 的部门ID 'ZJ0000101' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:43.742 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '丁云' 的部门ID 'ZJ0000101' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:43.821 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '许子扬' 的部门ID 'ZJ0000101' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:43.901 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '左啸寅' 的部门ID 'ZJ0000101' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:43.982 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '黄海俊' 的部门ID 'ZJ0000102' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:44.395 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王海龙' 的部门ID 'X49060100' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:44.582 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郭小石' 的部门ID 'X63170002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:44.645 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘家勇' 的部门ID 'X76030002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:44.715 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周京鹏' 的部门ID 'X63170001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:48.901 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王森' 的部门ID 'ZB3600002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:49.101 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王文财' 的部门ID 'ZF2100007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:49.226 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王泽民' 的部门ID 'ZJ1100008' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:49.362 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘晓丹' 的部门ID 'ZF0900000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:49.564 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李文广' 的部门ID 'ZF2100003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:49.625 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '孙霞' 的部门ID 'ZF2100005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:49.701 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '潘力' 的部门ID 'ZF2100001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:50.244 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '邓传伟' 的部门ID '1679ca2fb2e44de2b370742d77a0449c' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:50.876 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘钊' 的部门ID 'ZJ0000097' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:50.941 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王威龙' 的部门ID 'ZJ0000092' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:51.010 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张翔' 的部门ID 'ZJ0000102' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:51.082 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨有业' 的部门ID 'ZJ0000093' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:51.143 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王静娴' 的部门ID 'ZJ0000101' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:51.203 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨铭训' 的部门ID 'ZJ0000101' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:51.263 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '胡再华' 的部门ID 'X41000100' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:54.807 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王志伟' 的部门ID 'd9a332271eb04e49815974f87372c16f' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:54.955 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '翟春梅' 的部门ID 'ZF0900000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:55.228 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈后斌' 的部门ID 'ZB3600004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:56.368 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杜薇' 的部门ID 'XB5000200' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:54:56.998 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '黄寰' 的部门ID 'ZI0000004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:00.035 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '沈文联' 的部门ID 'ZN2900004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:01.541 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张大豹' 的部门ID 'X63170002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:01.781 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '许加国' 的部门ID 'X63180003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:02.600 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '薛多' 的部门ID 'ZB0300002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:03.261 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周健' 的部门ID 'X76020001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:03.397 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李宏兵' 的部门ID 'X34040004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:04.061 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '邵仁志' 的部门ID 'ZK0100029' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:05.181 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '袁巍' 的部门ID 'ZI0000005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:07.321 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '马智勇' 的部门ID 'XC2000100' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:08.021 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈超' 的部门ID 'XC2000500' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:08.662 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '夏光辉' 的部门ID 'd4f269d8365e48498167e71f134e2531' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:08.803 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '戴香港' 的部门ID 'ZJ0000099' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:08.996 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张珂欣' 的部门ID 'ZJ0000101' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:09.061 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李威' 的部门ID 'ZJ0000097' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:09.141 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李陶' 的部门ID 'ZJ0000092' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:09.203 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '左崔阳' 的部门ID 'ZJ0000097' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:09.263 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '吴帅' 的部门ID 'ZJ0000097' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:09.328 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈逍遥' 的部门ID 'ZJ0000101' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:09.396 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈勤' 的部门ID 'ZJ0000095' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:09.461 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王存' 的部门ID 'ZJ0000096' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:09.542 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '程炜翔' 的部门ID 'ZJ0000101' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:09.621 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘浩歆' 的部门ID 'ZJ0000101' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:09.682 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '谷茂彧' 的部门ID 'ZJ0000100' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:09.756 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '惠杨阳' 的部门ID 'ZJ0000100' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:09.836 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '鲍鹏' 的部门ID 'ZJ0000096' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:09.901 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '田明杭' 的部门ID 'ZJ0000097' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:09.962 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '彭亮雄' 的部门ID 'ZJ0000100' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:10.035 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李蕊' 的部门ID 'ZJ0000095' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:10.101 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周家宁' 的部门ID 'ZJ0000102' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:10.161 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '高军华' 的部门ID 'ZJ0000099' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:10.224 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '高奇慧' 的部门ID 'ZJ0000101' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:10.290 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '秦昊' 的部门ID 'ZJ0000092' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:10.382 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈林' 的部门ID 'ZJ0000100' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:10.443 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '吴浩' 的部门ID 'ZJ0000101' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:11.182 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '房栗德' 的部门ID 'ZB1700001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:11.682 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '吴卫东' 的部门ID 'ZB0300001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:15.005 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '赵兵' 的部门ID 'ZJ0100032' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:16.436 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐立军' 的部门ID 'ZJ0000096' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:16.523 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陆阳' 的部门ID 'X41000002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:16.941 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '曹志' 的部门ID 'X41000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:17.162 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘明' 的部门ID 'ZJ0000097' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:17.221 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周航' 的部门ID 'ZJ0000097' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:17.301 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '招秀鹏' 的部门ID 'ZJ0000102' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:17.381 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '余子恒' 的部门ID 'ZJ0000101' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:17.461 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '胡海锐' 的部门ID 'ZJ0000093' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:17.541 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨碧玉' 的部门ID 'ZJ0000101' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:17.601 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '董诚诚' 的部门ID 'ZJ0000096' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:17.662 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈迪鑫' 的部门ID 'ZJ0000097' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:17.741 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李杰' 的部门ID 'ZJ0000101' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:18.676 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '曹根成' 的部门ID 'ZB0400002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:20.928 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '毛雨倩' 的部门ID 'XB5000200' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:21.981 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张治欧' 的部门ID '1917c82911634ee09fd59b2ab5ab3758' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:22.448 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '孙红' 的部门ID 'X57020000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:23.396 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨俊柱' 的部门ID '1917c82911634ee09fd59b2ab5ab3758' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:23.981 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周建' 的部门ID '4f7e75fa317c482faf22ff48a762ef96' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:25.080 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '鲁军' 的部门ID 'b719acaa280a46ad82dc16023a1aa115' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:25.523 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '赵珂迪' 的部门ID 'ZN6100002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:25.803 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '孙浩杰' 的部门ID '32a0a2d0eeab4442abd6b5f108a3bc73' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:26.489 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李铮' 的部门ID 'b719acaa280a46ad82dc16023a1aa115' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:29.562 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王健' 的部门ID 'X76010000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:30.142 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘庆' 的部门ID 'ZA0900006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:35.849 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张柏强' 的部门ID 'XB7000100' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:37.341 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王鹏鹏' 的部门ID '9d7839db15ef4253b2b9ce6bd6965b0a' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:37.941 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陆誉' 的部门ID 'X63170002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:38.121 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '胡晋' 的部门ID 'X63190011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:38.520 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '胡家强' 的部门ID 'ZM0000066' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:39.010 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '丁浩然' 的部门ID '32a0a2d0eeab4442abd6b5f108a3bc73' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:40.234 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈刚' 的部门ID 'X76020002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:40.301 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '白素秋' 的部门ID 'ZF1700001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:40.582 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '田一飞' 的部门ID 'ZB3600005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:43.502 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '魏转转' 的部门ID '886b1946586f46a1adc075d00078fa72' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:43.849 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '经文' 的部门ID 'ZB3400002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:44.941 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱琼琼' 的部门ID 'X85030001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:47.995 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王云' 的部门ID 'X34020000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:48.061 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陆俊' 的部门ID 'c2f7b989434541dcafe2b34edea126e3' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:48.141 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '成功' 的部门ID 'c2f7b989434541dcafe2b34edea126e3' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:51.156 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '江艳' 的部门ID 'X34040002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:51.581 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈东' 的部门ID 'X34040004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:51.643 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张绍福' 的部门ID 'X34040005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:54.955 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '崔曼琪' 的部门ID 'X63190013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:55.940 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘卫' 的部门ID 'X76010000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:57.141 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郭勇' 的部门ID 'X76020002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:57.981 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '冯琦' 的部门ID 'X76020001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:58.061 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杜振钢' 的部门ID 'X76020001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:55:58.221 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐宁' 的部门ID 'X76030002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:00.061 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '崔靖' 的部门ID 'X48020001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:01.196 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '司文' 的部门ID 'XC2000500' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:01.421 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王招岚' 的部门ID 'XB7000100' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:02.161 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '龚祥岚' 的部门ID '6db8e90e21194d3eae6feb5e208da892' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:03.501 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '侯军' 的部门ID 'ZJ0100032' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:03.781 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘小常' 的部门ID 'X66080000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:05.569 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '赵文杰' 的部门ID '1917c82911634ee09fd59b2ab5ab3758' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:06.284 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '牛燕' 的部门ID 'X34040006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:06.925 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '石静' 的部门ID 'X74010000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:07.003 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐重来' 的部门ID 'X34010002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:07.635 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '钱勇' 的部门ID 'X76020001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:07.701 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '吴东' 的部门ID 'X76020001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:08.301 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王旭' 的部门ID 'ZB3600004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:08.620 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周平' 的部门ID 'X34060000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:09.682 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '巴川浩' 的部门ID 'ZB1400002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:09.915 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '林宁' 的部门ID 'c2f7b989434541dcafe2b34edea126e3' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:12.061 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陆晴晴' 的部门ID 'XC2000400' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:12.444 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张振双' 的部门ID 'X63170002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:14.421 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈伟' 的部门ID 'ZI0200019' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:16.342 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王辉' 的部门ID 'ZI1100003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:16.781 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '钱国刚' 的部门ID 'ZB3800000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:18.002 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '梁启磊' 的部门ID 'c2f7b989434541dcafe2b34edea126e3' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:18.820 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '金乐乐' 的部门ID 'ZF0900000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:20.782 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '祝有亮' 的部门ID 'ZI0500010' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:20.860 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '吕静' 的部门ID 'ZI0000011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:20.929 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '丁明龙' 的部门ID 'ZI0100040' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:21.101 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '廖航' 的部门ID 'ZI0500004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:21.181 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王蕾' 的部门ID 'ZI0500004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:21.261 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '印亚军' 的部门ID 'ZI0500010' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:21.401 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘凯' 的部门ID 'ZI0000002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:21.461 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李颜博' 的部门ID 'ZI0000029' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:21.541 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '姚斌' 的部门ID 'ZI0500016' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:21.621 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '蔡拥政' 的部门ID 'ZI0000024' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:21.700 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '吴兆成' 的部门ID 'ZF1600007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:21.762 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨晓童' 的部门ID 'ZI0000002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:21.837 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郭家军' 的部门ID 'ZI0500004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:22.142 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '牛涛' 的部门ID '5490e88de8d347789d557e08487d3352' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:23.960 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘承' 的部门ID 'c2f7b989434541dcafe2b34edea126e3' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:24.261 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘云静' 的部门ID 'ZZ0100001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:26.781 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '付钢' 的部门ID 'ZB1600005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:27.101 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '胡道峰' 的部门ID 'ZM0000073' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:27.764 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '秦若臻' 的部门ID 'XC2000402' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:27.923 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李杨' 的部门ID 'XB5000200' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:30.421 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '潘太标' 的部门ID 'X76030002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:30.501 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '吴云宁' 的部门ID 'X63190012' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:30.941 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱亮' 的部门ID 'XC1000300' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:31.581 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱炎玮' 的部门ID 'XC5000200' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:31.942 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王玉婷' 的部门ID 'ZZ0700007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:32.582 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郑翔飞' 的部门ID 'X76020002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:32.796 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '赵立庆' 的部门ID 'X85030001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:32.862 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张建中' 的部门ID 'X76010000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:33.076 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈行凯' 的部门ID 'c2f7b989434541dcafe2b34edea126e3' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:33.940 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '黄乐华' 的部门ID 'ZF1600007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:34.235 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱长标' 的部门ID '1917c82911634ee09fd59b2ab5ab3758' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:35.861 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张蕊' 的部门ID 'X63190011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:37.143 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张林芳' 的部门ID 'ZB3400006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:37.421 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '赵天齐' 的部门ID 'b719acaa280a46ad82dc16023a1aa115' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:37.914 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李颗' 的部门ID 'XC2000100' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:38.061 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈建' 的部门ID 'ZB4200005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:38.847 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '孙成成' 的部门ID 'ZB0300002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:39.581 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '巩文雪' 的部门ID 'ZB0300002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:39.721 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '毛啟清' 的部门ID 'X34030000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:39.796 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '翁传旺' 的部门ID 'X76030002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:40.581 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '蔡德仓' 的部门ID 'f1cb750b414f47bfadec9ebbc9efeb5f' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:43.402 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杜淑梅' 的部门ID 'X02170002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:43.560 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '黄晓娟' 的部门ID 'XC2000402' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:45.363 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '黄华顺' 的部门ID 'ZB3700007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:48.356 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '华亮明' 的部门ID 'ZI0000024' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:48.420 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '汪学宝' 的部门ID 'ZI0100040' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:48.555 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '舒金龙' 的部门ID 'ZI0000011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:48.621 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张晓勇' 的部门ID 'ZI0000002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:48.701 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '胡思捷' 的部门ID 'ZI0100001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:48.762 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '曹健' 的部门ID 'ZI0500004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:48.821 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '赵辉' 的部门ID 'X63190012' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:54.860 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '巩世超' 的部门ID 'ZB3600008' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:55.481 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '魏加东' 的部门ID '6430df941d294dca9b219bc66532bba8' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:56.721 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐欣' 的部门ID 'ZR0100000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:56:57.355 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '吴倩' 的部门ID 'ZZ0600003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:00.877 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '田冲' 的部门ID 'XC2000000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:01.181 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '许燕' 的部门ID 'ZZ0100001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:01.475 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '马忠柏' 的部门ID 'X76020002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:01.717 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '万里乐' 的部门ID 'ZF1300004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:03.323 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '薛华' 的部门ID 'ZB0300008' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:03.397 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '高博睿' 的部门ID 'X76020001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:03.463 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '曹华' 的部门ID 'X76020002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:05.023 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '于颖' 的部门ID 'ZZ0500002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:06.402 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '夏雷' 的部门ID 'ZB4200005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:10.079 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '费玲玲' 的部门ID 'ZB3400007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:10.155 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '彭峰' 的部门ID 'ZU1500000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:11.142 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王亮' 的部门ID 'X63190013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:11.360 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐乐乐' 的部门ID 'ZB2300013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:15.800 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '毛文斌' 的部门ID 'X41000100' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:16.662 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘永俊' 的部门ID 'X47070202' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:17.442 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '叶慧' 的部门ID 'ZB1000014' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:22.101 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱勇' 的部门ID 'c5a2ee152b784f9f91ed6a6fb7382c8e' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:27.224 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '马莉萍' 的部门ID 'ZJ0200000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:28.661 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐永圣' 的部门ID 'ZG0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:29.180 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '韩保华' 的部门ID 'ZB3800003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:32.262 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '林琛' 的部门ID 'ZR0200010' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:32.341 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张嘉伟' 的部门ID 'ZK0100046' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:33.140 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘克' 的部门ID 'ZI0000004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:33.443 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '丁辉芳' 的部门ID 'ZF2000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:33.581 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '沈言亮' 的部门ID 'ZB2300013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:33.667 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '金家珍' 的部门ID 'ZB1000004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:33.802 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘兴梅' 的部门ID 'ZM0000053' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:35.380 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '孙晓勇' 的部门ID '4f7e75fa317c482faf22ff48a762ef96' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:35.461 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '罗晶珺' 的部门ID 'XC2000500' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:35.860 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '孔祥勇' 的部门ID 'X63170004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:36.276 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王清' 的部门ID 'X34010002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:37.380 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '袁海军' 的部门ID 'X63180004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:38.220 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王晓甦' 的部门ID 'X76030002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:38.280 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '孙百文' 的部门ID 'X63170002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:38.901 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '苏钢' 的部门ID 'ZK1400007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:38.962 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '芮逸明' 的部门ID 'ZB0300001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:39.181 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '赵宁' 的部门ID 'ZK1400003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:39.395 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '林森' 的部门ID 'X63170002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:40.461 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '曾磊' 的部门ID 'ZI0200008' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:40.542 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李锐' 的部门ID 'ZM0100023' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:41.568 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '许葛彬' 的部门ID 'ZB3400003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:41.637 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐兴福' 的部门ID 'ZB0300001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:44.063 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈超' 的部门ID 'ZA0900005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:49.060 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '姚家勇' 的部门ID 'ZF0700003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:49.340 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '林娟' 的部门ID 'XC2000500' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:52.481 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张晓东' 的部门ID 'c2f7b989434541dcafe2b34edea126e3' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:54.943 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '林国捷' 的部门ID 'ZB3600012' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:56.280 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘毛毛' 的部门ID 'X43010000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:57:57.941 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王宏旭' 的部门ID 'XC2000400' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:00.942 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '沈悦颖' 的部门ID 'ZB0300006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:01.728 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '谢俏' 的部门ID 'ZB0100000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:01.909 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '邱加博' 的部门ID 'XC1000200' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:05.390 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '潘佩君' 的部门ID 'ZB0300002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:08.317 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '冯健' 的部门ID 'ZF2000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:12.122 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '胡锦' 的部门ID 'ZF2000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:13.314 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '屠昱佳' 的部门ID 'ZR0200001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:13.381 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '韩妍' 的部门ID 'XB5000100' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:14.341 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘小臣' 的部门ID 'bea498ab2afc46a9ae2460db0272d2d1' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:14.620 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '黄厚林' 的部门ID 'ZB0300003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:15.201 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '丁可欣' 的部门ID 'X63190011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:15.643 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '韩成明' 的部门ID 'X63170004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:16.821 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '唐新民' 的部门ID 'ZB0400000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:16.955 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '钱君一' 的部门ID 'X74030001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:17.722 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '宋炳澄' 的部门ID 'X64080501' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:18.161 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '汪保太' 的部门ID '32a0a2d0eeab4442abd6b5f108a3bc73' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:20.209 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐光龙' 的部门ID 'ZA0400000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:20.288 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈诚' 的部门ID 'ZI0500004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:21.522 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '黄一新' 的部门ID 'ZB2900001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:22.500 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '谢田' 的部门ID 'ZJ1100007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:22.563 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '黎氏青花' 的部门ID 'ZF0500012' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:22.715 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '曹沁雪' 的部门ID 'ZF0500007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:22.782 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '戴博文' 的部门ID 'ZF0500002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:22.940 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '胡徐怡' 的部门ID 'ZJ1100007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:23.020 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '吴超' 的部门ID 'ZJ1100007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:23.102 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '崔孝印' 的部门ID 'ZF0500011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:24.635 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '赵勇' 的部门ID '1917c82911634ee09fd59b2ab5ab3758' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:26.820 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '洪波' 的部门ID '1917c82911634ee09fd59b2ab5ab3758' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:27.523 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '秦桂平' 的部门ID '43776ed897b34029ae494a82235f8cce' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:28.354 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王伟祥' 的部门ID 'ZB4200001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:29.760 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨美晨' 的部门ID 'ZF0700003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:31.435 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '柏颖' 的部门ID 'X02130001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:32.180 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张宇' 的部门ID 'ZG0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:32.766 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '成强' 的部门ID 'c3e83043149b47c9a046a2c0cfa70dfa' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:35.421 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘兵' 的部门ID '8f45f98e1dd746b5b81bc0229d905ac3' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:36.741 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '任得强' 的部门ID 'ZZ0800021' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:37.996 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '邹青' 的部门ID 'X63180001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:38.141 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '高超' 的部门ID 'X63180002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:40.169 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱永峰' 的部门ID 'X76020001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:40.365 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '巨文贵' 的部门ID 'ZB0400002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:41.007 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周金宝' 的部门ID 'X41000400' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:41.302 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郭静婷' 的部门ID 'ZB0300002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:41.381 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '仲子昂' 的部门ID 'ZB2300017' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:43.980 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐春来' 的部门ID 'XC2000000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:45.044 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱佳佳' 的部门ID 'ZB0300005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:45.264 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈文静' 的部门ID 'X34070000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:46.662 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '赵燕' 的部门ID 'ZB3300010' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:47.180 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '袁媛' 的部门ID '438fb1fc5c954ddc9364348247f59975' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:49.701 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '汤荣兵' 的部门ID 'ZI0200034' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:50.402 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '胡亮' 的部门ID 'ZK1400008' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:52.341 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '蒋杰' 的部门ID 'X76030002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:54.239 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李震' 的部门ID '1917c82911634ee09fd59b2ab5ab3758' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:54.315 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '包祺' 的部门ID 'ZB0300005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:54.461 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '蒋钧惠' 的部门ID 'ZB3800006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:54.527 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '侯昕雨' 的部门ID 'ZB0300005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:54.600 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张林' 的部门ID 'ZB3800006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:55.901 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '姜何新' 的部门ID 'ZF1500006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:55.980 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '虞勇坚' 的部门ID '438fb1fc5c954ddc9364348247f59975' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:56.162 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '薛辰' 的部门ID 'ZB3600016' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:57.742 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐林明' 的部门ID '32a0a2d0eeab4442abd6b5f108a3bc73' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:58.021 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '高平' 的部门ID 'X63170005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:59.268 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '豆生源' 的部门ID 'ZB3600012' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:58:59.434 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '董鸣一' 的部门ID 'ZG0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:00.860 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '於宁安' 的部门ID 'X74020005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:00.940 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王华' 的部门ID 'ZF0500008' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:02.884 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '蔡红发' 的部门ID 'X42060014' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:02.962 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '方凯' 的部门ID 'f589b456432644a482bfa77bd724e541' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:03.763 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨永强' 的部门ID '32a0a2d0eeab4442abd6b5f108a3bc73' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:04.153 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '景诚' 的部门ID 'X42050009' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:06.762 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王东海' 的部门ID '43776ed897b34029ae494a82235f8cce' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:07.700 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '孙明' 的部门ID 'ZZ0100001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:08.780 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '耿新健' 的部门ID '43776ed897b34029ae494a82235f8cce' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:12.460 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李发周' 的部门ID 'X42050011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:12.660 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '高桂东' 的部门ID '438fb1fc5c954ddc9364348247f59975' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:15.123 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨裕国' 的部门ID 'ZB1000022' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:15.700 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周敏' 的部门ID 'ZI0200008' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:16.220 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘平' 的部门ID '8d8994033edb4494a781b9de504ce730' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:17.523 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '吴双' 的部门ID 'ZB3600012' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:17.677 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张杰' 的部门ID 'ZR0200001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:19.323 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '梁圣博' 的部门ID 'XC2000100' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:20.340 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨松' 的部门ID '5490e88de8d347789d557e08487d3352' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:20.600 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李晓兵' 的部门ID 'X42050009' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:21.020 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '孟玲' 的部门ID 'ZB3800004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:21.100 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '潘承峰' 的部门ID 'ZB0300005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:21.180 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '商稳稳' 的部门ID 'ZB3600007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:21.241 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '吴甜梦' 的部门ID 'ZB3600016' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:21.600 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈光昊' 的部门ID 'X42050011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:21.660 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈道顺' 的部门ID 'X42050011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:21.820 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '于小方' 的部门ID 'ZB3800001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:21.983 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈纪文' 的部门ID 'ZB0300005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:22.044 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '宋科' 的部门ID 'ZB0300005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:22.880 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '孙鑫' 的部门ID 'ZL0003000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:23.034 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '茅坚鑫' 的部门ID 'XB5000100' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:23.582 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨昌强' 的部门ID 'X66080000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:29.180 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '薛文雄' 的部门ID 'ZB3600012' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:29.260 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '章梓阳' 的部门ID 'ZB0300005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:30.421 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李建华' 的部门ID 'ZB2600010' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:31.321 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘呈军' 的部门ID 'X63170002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:31.460 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王红慧' 的部门ID 'ZZ0100007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:31.700 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '高承进' 的部门ID 'ZB2600010' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:32.141 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '冯刚' 的部门ID 'ZB2600010' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:34.640 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王强' 的部门ID 'b719acaa280a46ad82dc16023a1aa115' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:34.874 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱统方' 的部门ID '5490e88de8d347789d557e08487d3352' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:37.683 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周志高' 的部门ID 'ZB3400001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:38.100 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘雯' 的部门ID 'ZB3700008' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:38.581 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '魏波琴' 的部门ID 'ZJ0000063' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:39.180 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '黄晓强' 的部门ID 'ZL0002000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:39.401 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '吴学文' 的部门ID 'XB7000100' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:39.620 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '闫安' 的部门ID 'ZZ0800006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:39.767 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '樊圳' 的部门ID 'ZB3400004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:41.300 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '任森' 的部门ID '32a0a2d0eeab4442abd6b5f108a3bc73' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:41.660 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '宋烈炎' 的部门ID 'X74030001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:41.883 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈祥国' 的部门ID 'f1cb750b414f47bfadec9ebbc9efeb5f' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:50.107 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '高式林' 的部门ID 'ZG0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:51.235 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '卞启东' 的部门ID 'c3e83043149b47c9a046a2c0cfa70dfa' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:51.620 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘亚洲' 的部门ID 'ZB4200005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:51.780 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '于敏' 的部门ID 'ZF1800003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:52.100 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘世仁' 的部门ID 'ZB0400001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:52.580 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张孜屹' 的部门ID 'ZF0500020' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:56.020 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '黎德争' 的部门ID 'ZK0100045' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:57.199 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王剑松' 的部门ID 'ZF0900000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:57.621 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王语' 的部门ID 'b719acaa280a46ad82dc16023a1aa115' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:58.354 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘龙宝' 的部门ID 'b1f038e8ae4147c5ba9cb06fe52d4867' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:59.780 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王攀峰' 的部门ID 'ZB4200005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 10:59:59.841 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '石建兵' 的部门ID 'ZB4200002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:00.194 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王志明' 的部门ID 'ZB4200005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:04.420 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨洋' 的部门ID 'ZF1300000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:04.602 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '夏鑫' 的部门ID 'ZF0500003' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:04.741 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李珍爱' 的部门ID 'ZB2300012' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:04.880 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 'Ivannie' 的部门ID 'ZB3600012' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:06.314 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王庆' 的部门ID 'X42050011' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:06.540 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李成龙' 的部门ID 'X34040006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:07.060 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '韩朋军' 的部门ID 'ZB4200002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:08.280 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈昌' 的部门ID 'XC2000300' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:08.341 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张继东' 的部门ID 'e0f3561e91d4459fafce3047271a233e' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:09.661 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '师诺' 的部门ID 'ZB0300004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:10.001 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郁连' 的部门ID 'ZG0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:13.340 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '徐朔' 的部门ID '32a0a2d0eeab4442abd6b5f108a3bc73' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:13.420 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘春东' 的部门ID 'X63180002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:15.101 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '荣一鸣' 的部门ID 'ZK0100034' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:15.180 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周力' 的部门ID 'ZK1400018' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:15.260 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郑明富' 的部门ID 'ZK0100027' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:15.660 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '沈晓强' 的部门ID 'X41000100' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:15.819 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '宦叶青' 的部门ID 'ZL0000009' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:15.980 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨然' 的部门ID 'ZL0000009' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:16.060 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '许明吉' 的部门ID 'ZA0400000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:16.219 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '蔡力红' 的部门ID 'ZB0300005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:16.435 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '滑静' 的部门ID 'ZB0300002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:16.501 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '谷文萍' 的部门ID 'ZB0300004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:16.740 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '戴湘乾' 的部门ID 'ZB0300002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:17.000 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '金羊' 的部门ID 'ZB3400006' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:18.242 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘玉林' 的部门ID 'b719acaa280a46ad82dc16023a1aa115' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:22.460 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘子铭' 的部门ID 'X43010000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:22.700 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '吴忠海' 的部门ID 'ZR0200000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:22.780 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张光明' 的部门ID 'X34040002' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:22.997 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '孔宪祥' 的部门ID 'c5a2ee152b784f9f91ed6a6fb7382c8e' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:23.061 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '陈钢' 的部门ID 'c94e12d7ae5a415c9ff9ca515d7ab38d' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:23.460 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '宋效慧' 的部门ID 'ZB3300004' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:27.699 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王婧' 的部门ID 'ZL0004000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:27.980 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '黄立新' 的部门ID 'ZG0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:28.043 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '恽华伟' 的部门ID 'ZG0000001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:32.060 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '甄天云' 的部门ID 'ZB1700005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:35.568 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '何熙' 的部门ID 'ZL0000009' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:36.000 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杜磊' 的部门ID '1917c82911634ee09fd59b2ab5ab3758' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:36.074 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '娄序良' 的部门ID '3b88b0747961446b8b9dbbe45dd258a8' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:36.460 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '秦强伟' 的部门ID 'ZB3300001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:37.821 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张贤山' 的部门ID 'XC2000402' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:38.420 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '朱炎玮' 的部门ID 'ZB1000013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:38.788 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '刘严明' 的部门ID 'ZB2300007' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:39.620 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '杨琨' 的部门ID 'ZK0100036' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:40.820 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '赵建国' 的部门ID 'X34040001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:42.529 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '林飞' 的部门ID 'ZU1500000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:42.788 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '张晓强' 的部门ID 'ZB4200005' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:43.364 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '郭东升' 的部门ID 'X85030001' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:46.087 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '王延森' 的部门ID 'XC2000300' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:47.139 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '胡祥' 的部门ID 'ZF0700013' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:47.380 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '李家跃' 的部门ID 'X42070009' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:48.420 [http-nio-8285-exec-1] WARN  c.d.p.t.u.service.impl.UserImportServiceImpl - 用户 '周林' 的部门ID 'ZR0200000' 在映射文件中不存在，跳过此用户的部门设置
2025-08-02 11:00:50.674 [http-nio-8285-exec-1] INFO  c.d.p.t.u.service.impl.UserImportServiceImpl - 成功导入/更新了 11288 个用户
2025-08-02 14:12:23.509 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-02 14:12:23.699 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
