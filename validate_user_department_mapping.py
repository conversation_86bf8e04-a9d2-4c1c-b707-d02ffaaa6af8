#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户部门关联验证工具
对比CSV文件中的关联关系与数据库SQL文件中的实际关联关系
"""

import csv
import re
from collections import defaultdict

def parse_sql_insert_statements(sql_file):
    """
    解析SQL文件中的INSERT语句，提取数据
    
    Args:
        sql_file: SQL文件路径
        
    Returns:
        list: 解析出的数据记录列表
    """
    records = []
    
    print(f"正在解析SQL文件: {sql_file}")
    
    try:
        with open(sql_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 查找INSERT语句的模式
        insert_pattern = r"INSERT INTO.*?VALUES\s*\((.*?)\);"
        matches = re.findall(insert_pattern, content, re.DOTALL | re.IGNORECASE)
        
        for match in matches:
            # 解析VALUES中的数据
            # 处理字符串中的引号和逗号
            values = []
            current_value = ""
            in_quotes = False
            quote_char = None
            
            i = 0
            while i < len(match):
                char = match[i]
                
                if not in_quotes:
                    if char in ["'", '"']:
                        in_quotes = True
                        quote_char = char
                        current_value += char
                    elif char == ',':
                        values.append(current_value.strip())
                        current_value = ""
                    else:
                        current_value += char
                else:
                    current_value += char
                    if char == quote_char:
                        # 检查是否是转义的引号
                        if i + 1 < len(match) and match[i + 1] == quote_char:
                            current_value += match[i + 1]
                            i += 1
                        else:
                            in_quotes = False
                            quote_char = None
                
                i += 1
            
            # 添加最后一个值
            if current_value.strip():
                values.append(current_value.strip())
            
            records.append(values)
            
    except Exception as e:
        print(f"解析SQL文件时出错: {e}")
        return []
    
    print(f"解析完成，共找到 {len(records)} 条记录")
    return records

def extract_database_users(user_sql_file):
    """
    从t_user.sql中提取用户数据
    
    Returns:
        dict: {user_id: {'user_name': name, 'organ_affiliation': org_id, 'account': account}}
    """
    print("=== 提取数据库用户数据 ===")
    
    records = parse_sql_insert_statements(user_sql_file)
    users = {}
    
    for record in records:
        if len(record) >= 10:  # 确保有足够的字段
            try:
                user_id = record[0].strip()
                user_name = record[1].strip().strip("'\"")
                is_del = record[2].strip().strip("'\"")
                origin_id = record[3].strip().strip("'\"")
                organ_affiliation = record[4].strip()
                account = record[5].strip().strip("'\"")
                
                # 只处理未删除的用户
                if is_del.lower() in ['f', 'false']:
                    users[user_id] = {
                        'user_name': user_name,
                        'organ_affiliation': organ_affiliation if organ_affiliation != 'NULL' else None,
                        'account': account,
                        'origin_id': origin_id if origin_id != 'NULL' else None
                    }
            except Exception as e:
                print(f"解析用户记录时出错: {e}, 记录: {record[:3]}")
                continue
    
    print(f"提取到 {len(users)} 个有效用户")
    return users

def extract_database_orgs(org_sql_file):
    """
    从t_org_structure.sql中提取组织数据
    
    Returns:
        dict: {org_id: {'organ_name': name, 'pre_id': parent_id}}
    """
    print("=== 提取数据库组织数据 ===")
    
    records = parse_sql_insert_statements(org_sql_file)
    orgs = {}
    
    for record in records:
        if len(record) >= 7:  # 确保有足够的字段
            try:
                org_id = record[0].strip()
                organ_name = record[1].strip().strip("'\"")
                pre_id = record[2].strip()
                is_del = record[4].strip().strip("'\"")
                
                # 只处理未删除的组织
                if is_del.lower() in ['f', 'false']:
                    orgs[org_id] = {
                        'organ_name': organ_name,
                        'pre_id': pre_id if pre_id != 'NULL' else None
                    }
            except Exception as e:
                print(f"解析组织记录时出错: {e}, 记录: {record[:3]}")
                continue
    
    print(f"提取到 {len(orgs)} 个有效组织")
    return orgs

def load_csv_mapping(csv_file):
    """
    加载CSV文件中的用户部门对应关系
    
    Returns:
        dict: {user_name: {'dept_name': name, 'user_no': no, 'dept_id': id}}
    """
    print("=== 加载CSV对应关系 ===")
    
    csv_mapping = {}
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for row in reader:
                user_name = row.get('人员姓名', '').strip()
                dept_name = row.get('部门名称', '').strip()
                user_no = row.get('用户工号', '').strip()
                dept_id = row.get('部门ID', '').strip()
                
                if user_name:
                    csv_mapping[user_name] = {
                        'dept_name': dept_name,
                        'user_no': user_no,
                        'dept_id': dept_id
                    }
                    
    except Exception as e:
        print(f"加载CSV文件时出错: {e}")
        return {}
    
    print(f"加载到 {len(csv_mapping)} 个用户的对应关系")
    return csv_mapping

def validate_mappings(db_users, db_orgs, csv_mapping):
    """
    验证数据库关联关系与CSV关联关系的一致性
    """
    print("=== 开始验证关联关系 ===")
    
    # 统计信息
    total_csv_users = len(csv_mapping)
    matched_users = 0
    name_matched = 0
    org_matched = 0
    not_in_db = 0
    org_mismatch = 0
    
    # 详细结果
    validation_results = []
    
    # 建立数据库用户名到用户信息的映射
    db_users_by_name = {}
    for user_id, user_info in db_users.items():
        user_name = user_info['user_name']
        if user_name not in db_users_by_name:
            db_users_by_name[user_name] = []
        db_users_by_name[user_name].append((user_id, user_info))
    
    print(f"数据库中有 {len(db_users_by_name)} 个不同的用户名")
    
    for csv_user_name, csv_info in csv_mapping.items():
        result = {
            'csv_user_name': csv_user_name,
            'csv_dept_name': csv_info['dept_name'],
            'csv_user_no': csv_info['user_no'],
            'csv_dept_id': csv_info['dept_id'],
            'status': 'unknown',
            'db_user_name': None,
            'db_dept_name': None,
            'db_org_id': None
        }
        
        # 检查用户是否在数据库中
        if csv_user_name in db_users_by_name:
            name_matched += 1
            
            # 可能有多个同名用户，选择第一个
            db_user_id, db_user_info = db_users_by_name[csv_user_name][0]
            
            result['db_user_name'] = db_user_info['user_name']
            result['db_org_id'] = db_user_info['organ_affiliation']
            
            # 检查组织关联
            if db_user_info['organ_affiliation'] and db_user_info['organ_affiliation'] in db_orgs:
                db_org_info = db_orgs[db_user_info['organ_affiliation']]
                result['db_dept_name'] = db_org_info['organ_name']
                
                # 比较部门名称（简单的包含关系检查）
                if (csv_info['dept_name'] in db_org_info['organ_name'] or 
                    db_org_info['organ_name'] in csv_info['dept_name'] or
                    csv_info['dept_name'] == db_org_info['organ_name']):
                    result['status'] = 'matched'
                    matched_users += 1
                    org_matched += 1
                else:
                    result['status'] = 'org_mismatch'
                    org_mismatch += 1
            else:
                result['status'] = 'no_org_in_db'
        else:
            result['status'] = 'not_in_db'
            not_in_db += 1
        
        validation_results.append(result)
    
    # 输出统计结果
    print(f"\n=== 验证结果统计 ===")
    print(f"CSV文件总用户数: {total_csv_users}")
    print(f"数据库中找到同名用户: {name_matched} ({name_matched/total_csv_users*100:.1f}%)")
    print(f"完全匹配（用户名+部门）: {matched_users} ({matched_users/total_csv_users*100:.1f}%)")
    print(f"用户存在但部门不匹配: {org_mismatch} ({org_mismatch/total_csv_users*100:.1f}%)")
    print(f"数据库中未找到用户: {not_in_db} ({not_in_db/total_csv_users*100:.1f}%)")
    
    # 保存详细验证结果
    output_file = "验证结果.csv"
    with open(output_file, 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['CSV用户名', 'CSV部门名', 'CSV工号', 'CSV部门ID', '验证状态', 
                        '数据库用户名', '数据库部门名', '数据库组织ID'])
        
        for result in validation_results:
            writer.writerow([
                result['csv_user_name'],
                result['csv_dept_name'],
                result['csv_user_no'],
                result['csv_dept_id'],
                result['status'],
                result['db_user_name'] or '',
                result['db_dept_name'] or '',
                result['db_org_id'] or ''
            ])
    
    print(f"\n详细验证结果已保存到: {output_file}")
    
    # 显示一些不匹配的例子
    print(f"\n=== 部门不匹配示例 (前5个) ===")
    mismatch_count = 0
    for result in validation_results:
        if result['status'] == 'org_mismatch' and mismatch_count < 5:
            print(f"用户: {result['csv_user_name']}")
            print(f"  CSV部门: {result['csv_dept_name']}")
            print(f"  数据库部门: {result['db_dept_name']}")
            print()
            mismatch_count += 1

def main():
    """主函数"""
    print("=== 用户部门关联验证工具 ===")
    
    # 文件路径
    user_sql_file = "t_user.sql"
    org_sql_file = "t_org_structure.sql"
    csv_file = "用户部门对应关系.csv"
    
    # 提取数据库数据
    db_users = extract_database_users(user_sql_file)
    db_orgs = extract_database_orgs(org_sql_file)
    
    # 加载CSV数据
    csv_mapping = load_csv_mapping(csv_file)
    
    # 验证关联关系
    validate_mappings(db_users, db_orgs, csv_mapping)
    
    print("\n=== 验证完成 ===")

if __name__ == "__main__":
    main()
