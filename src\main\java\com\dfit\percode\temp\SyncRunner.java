//package com.dfit.percode.temp;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.CommandLineRunner;
//import org.springframework.stereotype.Component;
//
///**
// * 同步工具运行器
// * 启动时自动执行数据同步
// */
//@Component
//public class SyncRunner implements CommandLineRunner {
//
//    @Autowired
//    private OrganizationSyncTool organizationSyncTool;
//
//    @Override
//    public void run(String... args) throws Exception {
//        // 检查是否需要执行同步
//        boolean shouldSync = false;
//
//        // 检查命令行参数
//        for (String arg : args) {
//            if ("--sync-org".equals(arg)) {
//                shouldSync = true;
//                break;
//            }
//        }
//
//        if (shouldSync) {
//            System.out.println("=== 开始执行组织架构数据同步 ===");
//
//            try {
//                OrganizationSyncTool.SyncResult result = organizationSyncTool.executeSync();
//
//                System.out.println("\n=== 同步结果 ===");
//                System.out.println("成功: " + result.isSuccess());
//                System.out.println("原始数据: " + result.getRawDataCount());
//                System.out.println("清洗数据: " + result.getCleanDataCount());
//                System.out.println("树节点数: " + result.getTreeNodeCount());
//                System.out.println("删除记录: " + result.getDeletedCount());
//                System.out.println("插入记录: " + result.getInsertedCount());
//                System.out.println("最终记录: " + result.getFinalCount());
//
//                if (result.isSuccess()) {
//                    System.out.println("✅ 数据同步成功！");
//                } else {
//                    System.out.println("❌ 数据同步失败: " + result.getErrorMessage());
//                }
//
//            } catch (Exception e) {
//                System.err.println("❌ 同步过程中发生异常: " + e.getMessage());
//                e.printStackTrace();
//            }
//
//            System.out.println("=== 同步完成 ===");
//        }
//    }
//}
