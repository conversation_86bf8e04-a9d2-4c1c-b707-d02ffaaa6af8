package com.dfit.percode.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dfit.percode.entity.TUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import org.apache.ibatis.annotations.MapKey;

import java.util.Map;

@Mapper
public interface TUserMapper extends BaseMapper<TUser> {

    @MapKey("organ_name")
    @Select("SELECT organ_name, id FROM t_org_structure")
    Map<String, Map<String, Object>> getAllDeptIdAndName();
}