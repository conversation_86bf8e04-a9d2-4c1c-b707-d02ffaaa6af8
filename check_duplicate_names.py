#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重名用户检查工具
分析数据库和CSV文件中的重名用户情况
"""

import csv
import re
from collections import defaultdict, Counter

def parse_sql_insert_statements(sql_file):
    """解析SQL文件中的INSERT语句"""
    records = []
    
    try:
        with open(sql_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        insert_pattern = r"INSERT INTO.*?VALUES\s*\((.*?)\);"
        matches = re.findall(insert_pattern, content, re.DOTALL | re.IGNORECASE)
        
        for match in matches:
            values = []
            current_value = ""
            in_quotes = False
            quote_char = None
            
            i = 0
            while i < len(match):
                char = match[i]
                
                if not in_quotes:
                    if char in ["'", '"']:
                        in_quotes = True
                        quote_char = char
                        current_value += char
                    elif char == ',':
                        values.append(current_value.strip())
                        current_value = ""
                    else:
                        current_value += char
                else:
                    current_value += char
                    if char == quote_char:
                        if i + 1 < len(match) and match[i + 1] == quote_char:
                            current_value += match[i + 1]
                            i += 1
                        else:
                            in_quotes = False
                            quote_char = None
                
                i += 1
            
            if current_value.strip():
                values.append(current_value.strip())
            
            records.append(values)
            
    except Exception as e:
        print(f"解析SQL文件时出错: {e}")
        return []
    
    return records

def analyze_database_duplicates():
    """分析数据库中的重名用户"""
    print("=== 分析数据库重名用户 ===")
    
    records = parse_sql_insert_statements("t_user.sql")
    
    # 用户名统计
    name_counts = Counter()
    name_details = defaultdict(list)
    
    for record in records:
        if len(record) >= 10:
            try:
                user_id = record[0].strip()
                user_name = record[1].strip().strip("'\"")
                is_del = record[2].strip().strip("'\"")
                origin_id = record[3].strip().strip("'\"")
                organ_affiliation = record[4].strip()
                account = record[5].strip().strip("'\"")
                
                # 只统计未删除的用户
                if is_del.lower() in ['f', 'false']:
                    name_counts[user_name] += 1
                    name_details[user_name].append({
                        'user_id': user_id,
                        'origin_id': origin_id if origin_id != 'NULL' else None,
                        'organ_affiliation': organ_affiliation if organ_affiliation != 'NULL' else None,
                        'account': account
                    })
            except Exception as e:
                continue
    
    # 找出重名用户
    duplicates = {name: count for name, count in name_counts.items() if count > 1}
    
    print(f"数据库总用户数: {sum(name_counts.values())}")
    print(f"唯一用户名数: {len(name_counts)}")
    print(f"重名用户名数: {len(duplicates)}")
    print(f"重名用户总数: {sum(duplicates.values())}")
    
    if duplicates:
        print(f"\n=== 重名用户详情 (前10个) ===")
        count = 0
        for name, dup_count in sorted(duplicates.items(), key=lambda x: x[1], reverse=True):
            if count >= 10:
                break
            print(f"\n用户名: {name} (共{dup_count}个)")
            for i, detail in enumerate(name_details[name], 1):
                print(f"  {i}. ID: {detail['user_id']}, 账号: {detail['account']}, "
                      f"部门ID: {detail['organ_affiliation']}, 原始ID: {detail['origin_id']}")
            count += 1
    
    return name_counts, name_details, duplicates

def analyze_csv_duplicates():
    """分析CSV文件中的重名用户"""
    print("\n=== 分析CSV重名用户 ===")
    
    name_counts = Counter()
    name_details = defaultdict(list)
    
    try:
        with open("用户部门对应关系.csv", 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for row in reader:
                user_name = row.get('人员姓名', '').strip()
                dept_name = row.get('部门名称', '').strip()
                user_no = row.get('用户工号', '').strip()
                dept_id = row.get('部门ID', '').strip()
                
                if user_name:
                    name_counts[user_name] += 1
                    name_details[user_name].append({
                        'dept_name': dept_name,
                        'user_no': user_no,
                        'dept_id': dept_id
                    })
                    
    except Exception as e:
        print(f"读取CSV文件时出错: {e}")
        return {}, {}, {}
    
    # 找出重名用户
    duplicates = {name: count for name, count in name_counts.items() if count > 1}
    
    print(f"CSV总用户数: {sum(name_counts.values())}")
    print(f"唯一用户名数: {len(name_counts)}")
    print(f"重名用户名数: {len(duplicates)}")
    print(f"重名用户总数: {sum(duplicates.values())}")
    
    if duplicates:
        print(f"\n=== CSV重名用户详情 (前10个) ===")
        count = 0
        for name, dup_count in sorted(duplicates.items(), key=lambda x: x[1], reverse=True):
            if count >= 10:
                break
            print(f"\n用户名: {name} (共{dup_count}个)")
            for i, detail in enumerate(name_details[name], 1):
                print(f"  {i}. 工号: {detail['user_no']}, 部门: {detail['dept_name']}, "
                      f"部门ID: {detail['dept_id']}")
            count += 1
    
    return name_counts, name_details, duplicates

def compare_duplicates(db_duplicates, csv_duplicates, db_details, csv_details):
    """对比数据库和CSV中的重名情况"""
    print("\n=== 重名用户对比分析 ===")
    
    # 找出共同的重名用户
    common_duplicates = set(db_duplicates.keys()) & set(csv_duplicates.keys())
    db_only_duplicates = set(db_duplicates.keys()) - set(csv_duplicates.keys())
    csv_only_duplicates = set(csv_duplicates.keys()) - set(db_duplicates.keys())
    
    print(f"数据库和CSV都有重名: {len(common_duplicates)}个用户名")
    print(f"仅数据库有重名: {len(db_only_duplicates)}个用户名")
    print(f"仅CSV有重名: {len(csv_only_duplicates)}个用户名")
    
    if common_duplicates:
        print(f"\n=== 共同重名用户分析 (前5个) ===")
        count = 0
        for name in sorted(common_duplicates):
            if count >= 5:
                break
            print(f"\n用户名: {name}")
            print(f"  数据库中有 {db_duplicates[name]} 个")
            print(f"  CSV中有 {csv_duplicates[name]} 个")
            
            # 显示详细信息
            print("  数据库详情:")
            for i, detail in enumerate(db_details[name], 1):
                print(f"    {i}. ID: {detail['user_id']}, 账号: {detail['account']}, "
                      f"部门ID: {detail['organ_affiliation']}")
            
            print("  CSV详情:")
            for i, detail in enumerate(csv_details[name], 1):
                print(f"    {i}. 工号: {detail['user_no']}, 部门: {detail['dept_name']}")
            
            count += 1
    
    return common_duplicates

def analyze_duplicate_impact():
    """分析重名对验证结果的影响"""
    print("\n=== 分析重名对验证的影响 ===")
    
    # 读取验证结果
    mismatch_users = []
    try:
        with open("验证结果.csv", 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for row in reader:
                if row.get('验证状态') == 'org_mismatch':
                    mismatch_users.append({
                        'name': row.get('CSV用户名'),
                        'csv_dept': row.get('CSV部门名'),
                        'db_dept': row.get('数据库部门名'),
                        'csv_user_no': row.get('CSV工号'),
                        'db_org_id': row.get('数据库组织ID')
                    })
    except Exception as e:
        print(f"读取验证结果时出错: {e}")
        return
    
    print(f"部门不匹配的用户总数: {len(mismatch_users)}")
    
    # 检查这些不匹配用户是否存在重名问题
    db_name_counts, db_name_details, db_duplicates = analyze_database_duplicates()
    
    duplicate_mismatch = []
    for user in mismatch_users:
        if user['name'] in db_duplicates:
            duplicate_mismatch.append(user)
    
    print(f"不匹配用户中存在重名的: {len(duplicate_mismatch)}个")
    print(f"重名导致不匹配的比例: {len(duplicate_mismatch)/len(mismatch_users)*100:.1f}%")
    
    if duplicate_mismatch:
        print(f"\n=== 重名导致不匹配的用户示例 (前5个) ===")
        for i, user in enumerate(duplicate_mismatch[:5], 1):
            print(f"{i}. {user['name']} (工号: {user['csv_user_no']})")
            print(f"   CSV部门: {user['csv_dept']}")
            print(f"   数据库部门: {user['db_dept']}")
            print(f"   数据库中该用户名有 {db_duplicates[user['name']]} 个记录")
            print()

def main():
    """主函数"""
    print("=== 重名用户检查工具 ===")
    
    # 分析数据库重名
    db_name_counts, db_name_details, db_duplicates = analyze_database_duplicates()
    
    # 分析CSV重名
    csv_name_counts, csv_name_details, csv_duplicates = analyze_csv_duplicates()
    
    # 对比重名情况
    common_duplicates = compare_duplicates(db_duplicates, csv_duplicates, 
                                         db_name_details, csv_name_details)
    
    # 分析重名对验证结果的影响
    analyze_duplicate_impact()
    
    print("\n=== 分析完成 ===")

if __name__ == "__main__":
    main()
