#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从ERP系统获取部门数据并与HR_DEPARTMENT.csv对比
"""

import requests
import xml.etree.ElementTree as ET
import csv
from datetime import datetime, timedelta
import json

def create_soap_xml(start_date, end_date):
    """创建SOAP XML请求体"""
    soap_template = '''<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
<soapenv:Header/>
<soapenv:Body>
<tem:GetOrgInfoFromMDM>
<!--Optional:-->
<tem:InXml><![CDATA[<?xml version="1.0" encoding="utf-8"?>
<I_DATAS><I_DATA>
<I_UDEF1>ERP</I_UDEF1>
<I_UDEF2>{start_date}</I_UDEF2>
<I_UDEF3>{end_date}</I_UDEF3>
<I_UDEF4></I_UDEF4>
</I_DATA></I_DATAS>]]></tem:InXml>
</tem:GetOrgInfoFromMDM>
</soapenv:Body>
</soapenv:Envelope>'''
    
    return soap_template.format(start_date=start_date, end_date=end_date)

def call_erp_api(start_date, end_date):
    """调用ERP接口获取部门数据"""
    url = "https://dmzesb.nisco.cn/dmzesb/XYTQZSJ/MDM/services/GetDatasFromMDMQuery"
    
    headers = {
        'Content-Type': 'text/xml;charset=UTF-8',
        'SOAPAction': ''  # 可能需要根据实际情况调整
    }
    
    xml_data = create_soap_xml(start_date, end_date)
    
    try:
        print(f"正在调用ERP接口...")
        print(f"URL: {url}")
        print(f"时间范围: {start_date} 到 {end_date}")
        
        response = requests.post(url, data=xml_data, headers=headers, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            return response.text
        else:
            print(f"请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def parse_erp_response(response_text):
    """解析ERP响应XML"""
    try:
        print("开始解析SOAP响应...")

        # 首先解析整个SOAP响应
        root = ET.fromstring(response_text)

        # 查找GetOrgInfoFromMDMResponse中的GetOrgInfoFromMDMResult
        namespaces = {
            'soap': 'http://schemas.xmlsoap.org/soap/envelope/',
            'tem': 'http://tempuri.org/'
        }

        # 查找响应结果
        result_element = root.find('.//tem:GetOrgInfoFromMDMResult', namespaces)

        if result_element is None:
            # 尝试不使用命名空间
            result_element = root.find('.//GetOrgInfoFromMDMResult')

        if result_element is None:
            print("未找到GetOrgInfoFromMDMResult元素")
            print("响应结构:")
            print_xml_structure(root, 0, 3)  # 打印前3层结构
            return []

        xml_content = result_element.text
        if not xml_content:
            print("GetOrgInfoFromMDMResult为空")
            return []

        print(f"提取的XML内容长度: {len(xml_content)}")

        # 解析内部XML内容
        inner_root = ET.fromstring(xml_content)
        departments = []

        # 根据实际XML结构解析部门数据
        # 常见的结构可能是 <ROOT><DATA>...</DATA></ROOT> 或类似
        for data_element in inner_root.findall('.//DATA'):
            dept = {}
            for child in data_element:
                dept[child.tag] = child.text if child.text else ''
            if dept:  # 只添加非空部门
                departments.append(dept)

        # 如果没找到DATA元素，尝试其他可能的结构
        if not departments:
            for item in inner_root.findall('.//*'):
                if len(list(item)) > 0:  # 有子元素的节点
                    dept = {}
                    for child in item:
                        dept[child.tag] = child.text if child.text else ''
                    if len(dept) > 3:  # 至少有几个字段才认为是部门数据
                        departments.append(dept)
                        break  # 找到一个样本就够了，避免重复

        print(f"解析到 {len(departments)} 个部门")
        if departments:
            print("第一个部门的字段:", list(departments[0].keys()))

        return departments

    except Exception as e:
        print(f"解析XML时出错: {e}")
        print(f"响应内容前1000字符: {response_text[:1000]}")
        return []

def print_xml_structure(element, level, max_level):
    """打印XML结构用于调试"""
    if level > max_level:
        return

    indent = "  " * level
    tag = element.tag.split('}')[-1] if '}' in element.tag else element.tag
    print(f"{indent}<{tag}>")

    for child in element:
        print_xml_structure(child, level + 1, max_level)

def convert_to_hr_format(erp_departments):
    """将ERP数据转换为HR_DEPARTMENT格式"""
    hr_departments = []
    
    for dept in erp_departments:
        hr_dept = {
            'ID': dept.get('ORGCODE', ''),  # 使用ORGCODE作为ID
            'STR_SN': dept.get('ORGCODE', ''),
            'STR_NAME': dept.get('ORGNAME', ''),
            'STR_DESC': dept.get('ORAALLNAME', ''),
            'FATHER_ID': dept.get('PNODECODE', ''),
            'DEPT_LEVEL': '',  # ERP可能没有这个字段
            'ISHISTORY': dept.get('ISHISTORY', '0')
        }
        hr_departments.append(hr_dept)
    
    return hr_departments

def load_existing_hr_data():
    """加载现有的HR_DEPARTMENT.csv数据"""
    hr_data = {}
    
    try:
        with open('HR_DEPARTMENT.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                hr_data[row['STR_SN']] = row
        
        print(f"加载现有HR数据: {len(hr_data)}条记录")
        return hr_data
        
    except FileNotFoundError:
        print("HR_DEPARTMENT.csv文件不存在")
        return {}

def compare_data(erp_data, hr_data):
    """对比ERP数据和HR数据"""
    print("\n=== 数据对比结果 ===")
    
    erp_codes = set(dept['STR_SN'] for dept in erp_data)
    hr_codes = set(hr_data.keys())
    
    # 统计
    only_in_erp = erp_codes - hr_codes
    only_in_hr = hr_codes - erp_codes
    common = erp_codes & hr_codes
    
    print(f"ERP中的部门数: {len(erp_codes)}")
    print(f"HR文件中的部门数: {len(hr_codes)}")
    print(f"共同部门数: {len(common)}")
    print(f"仅在ERP中: {len(only_in_erp)}")
    print(f"仅在HR文件中: {len(only_in_hr)}")
    
    # 显示差异
    if only_in_erp:
        print(f"\n仅在ERP中的部门 (前10个):")
        for code in list(only_in_erp)[:10]:
            erp_dept = next(d for d in erp_data if d['STR_SN'] == code)
            print(f"  {code}: {erp_dept['STR_NAME']}")
    
    if only_in_hr:
        print(f"\n仅在HR文件中的部门 (前10个):")
        for code in list(only_in_hr)[:10]:
            print(f"  {code}: {hr_data[code]['STR_NAME']}")
    
    # 检查共同部门的差异
    differences = []
    for code in common:
        erp_dept = next(d for d in erp_data if d['STR_SN'] == code)
        hr_dept = hr_data[code]
        
        if erp_dept['STR_NAME'] != hr_dept['STR_NAME']:
            differences.append({
                'code': code,
                'field': 'STR_NAME',
                'erp_value': erp_dept['STR_NAME'],
                'hr_value': hr_dept['STR_NAME']
            })
    
    if differences:
        print(f"\n共同部门中的差异 (前5个):")
        for diff in differences[:5]:
            print(f"  {diff['code']} {diff['field']}: ERP='{diff['erp_value']}' vs HR='{diff['hr_value']}'")

def save_erp_data(erp_data):
    """保存ERP数据到文件"""
    if not erp_data:
        print("没有ERP数据可保存")
        return
    
    filename = f"ERP_DEPARTMENTS_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    with open(filename, 'w', encoding='utf-8', newline='') as f:
        if erp_data:
            writer = csv.DictWriter(f, fieldnames=erp_data[0].keys())
            writer.writeheader()
            writer.writerows(erp_data)
    
    print(f"ERP数据已保存到: {filename}")

def main():
    """主函数"""
    print("=== ERP部门数据获取和对比工具 ===")
    
    # 设置时间范围（最近30天）
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    start_date_str = start_date.strftime("%Y/%m/%d 00:00:00")
    end_date_str = end_date.strftime("%Y/%m/%d 23:59:59")
    
    # 1. 调用ERP接口
    response_text = call_erp_api(start_date_str, end_date_str)
    
    if not response_text:
        print("无法获取ERP数据")
        return
    
    # 保存原始响应用于调试
    with open('erp_response.xml', 'w', encoding='utf-8') as f:
        f.write(response_text)
    print("原始响应已保存到: erp_response.xml")
    
    # 2. 解析ERP响应
    erp_departments = parse_erp_response(response_text)
    
    if not erp_departments:
        print("解析ERP响应失败")
        return
    
    # 3. 转换为HR格式
    hr_format_data = convert_to_hr_format(erp_departments)
    
    # 4. 保存ERP数据
    save_erp_data(hr_format_data)
    
    # 5. 加载现有HR数据
    existing_hr_data = load_existing_hr_data()
    
    # 6. 对比数据
    if existing_hr_data:
        compare_data(hr_format_data, existing_hr_data)
    
    print("\n=== 处理完成 ===")

if __name__ == "__main__":
    main()
