/*
 Navicat Premium Data Transfer

 Source Server         : ************_5432
 Source Server Type    : PostgreSQL
 Source Server Version : 90224
 Source Host           : ************:5432
 Source Catalog        : rbac
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 90224
 File Encoding         : 65001

 Date: 02/08/2025 09:01:39
*/


-- ----------------------------
-- Table structure for t_user
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_user";
CREATE TABLE "public"."t_user" (
  "id" int8 NOT NULL,
  "user_name" varchar(255) COLLATE "pg_catalog"."default",
  "is_del" bool,
  "origin_id" varchar(255) COLLATE "pg_catalog"."default",
  "organ_affiliation" int8,
  "account" varchar(255) COLLATE "pg_catalog"."default",
  "password" varchar(255) COLLATE "pg_catalog"."default",
  "is_disable" bool,
  "create_time" timestamp(6),
  "modify_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."t_user"."id" IS '人员信息id';
COMMENT ON COLUMN "public"."t_user"."user_name" IS '用户名称';
COMMENT ON COLUMN "public"."t_user"."is_del" IS '是否删除';
COMMENT ON COLUMN "public"."t_user"."origin_id" IS '原始关联id，用于外部系统对接';
COMMENT ON COLUMN "public"."t_user"."organ_affiliation" IS '组织归属，对应组织架构表中的id';
COMMENT ON COLUMN "public"."t_user"."account" IS '账户';
COMMENT ON COLUMN "public"."t_user"."password" IS '密码';
COMMENT ON COLUMN "public"."t_user"."is_disable" IS '是否停用';
COMMENT ON TABLE "public"."t_user" IS '人员信息表，与组织架构绑定';

-- ----------------------------
-- Records of t_user
-- ----------------------------
INSERT INTO "public"."t_user" VALUES (1936640367617249280, '文件管理员', 'f', NULL, 5009, '1001', 'e10adc3949ba59abbe56e057f20f883e', 'f', '2025-06-22 12:20:19.335999', '2025-07-02 04:24:28.767414');
INSERT INTO "public"."t_user" VALUES (1932639748158001152, '测试用户001', 't', NULL, NULL, '测试用户001', '123456', 'f', '2025-06-11 11:23:17.337668', '2025-06-11 18:11:21.214319');
INSERT INTO "public"."t_user" VALUES (6002, '李技术总监', 't', 'li_cto', NULL, NULL, 'password123', 'f', '2025-06-11 01:58:47.958483', '2025-06-11 18:23:35.418619');
INSERT INTO "public"."t_user" VALUES (6001, '张总经理', 't', 'zhang_ceo', 5001, 'zhang001', 'password123', 'f', '2025-06-11 01:58:47.958483', '2025-06-21 07:06:52.152498');
INSERT INTO "public"."t_user" VALUES (1936370058246885376, 'a', 't', NULL, 5008, 'a', 'a', 'f', '2025-06-21 18:26:12.559204', '2025-06-21 18:26:19.118878');
INSERT INTO "public"."t_user" VALUES (1934773648393113600, '测试人员', 't', NULL, 5008, 'admintest', '123456', 'f', '2025-06-17 08:42:38.791092', '2025-06-17 08:43:01.147842');
INSERT INTO "public"."t_user" VALUES (1938155631131365376, '超级管理员', 'f', NULL, 5004, 'admin', '14e1b600b1fd579f47433b88e8d85291', 'f', '2025-06-26 16:41:26.309519', '2025-07-24 09:43:39.114805');
INSERT INTO "public"."t_user" VALUES (1948199384810393600, 'test123', 't', NULL, 5004, 'test123', '25d55ad283aa400af464c76d713c07ad', 'f', '2025-07-24 09:51:43.79096', '2025-07-24 09:54:07.138742');
INSERT INTO "public"."t_user" VALUES (1935318005881901056, '123', 't', NULL, 5012, '123123', '123456', 'f', '2025-06-18 20:45:43.729772', '2025-06-20 13:46:53.960769');
INSERT INTO "public"."t_user" VALUES (1935155281398992896, 'sdf', 't', NULL, NULL, 'asdf', 'asdf', 'f', '2025-06-18 09:59:07.189611', '2025-06-20 13:46:57.308734');
INSERT INTO "public"."t_user" VALUES (1935946418258841600, '小韩', 't', NULL, 5008, '456', '456', 't', '2025-06-20 14:22:48.91593', '2025-06-20 14:22:59.743791');
INSERT INTO "public"."t_user" VALUES (6003, '王产品总监', 't', 'wang_cpo', 5003, 'wang.cpo', 'password123', 't', '2025-06-11 01:58:47.958483', '2025-06-21 07:06:05.422033');
INSERT INTO "public"."t_user" VALUES (6010, '王运维工程师', 't', 'wang_ops', 5010, 'wang.ops', 'password123', 'f', '2025-06-11 01:58:47.958483', '2025-06-21 07:06:07.503522');
INSERT INTO "public"."t_user" VALUES (6006, '孙测试经理', 't', 'sun_qa_mgr', 5009, 'sun.qa', 'password123', 't', '2025-06-11 01:58:47.958483', '2025-06-21 07:06:09.561032');
INSERT INTO "public"."t_user" VALUES (6009, '郑测试工程师', 't', 'zheng_qa', 5009, 'zheng.qa', 'password123', 't', '2025-06-11 01:58:47.958483', '2025-06-21 07:06:11.336351');
INSERT INTO "public"."t_user" VALUES (6005, '钱后端经理', 't', 'qian_be_mgr', 5008, 'qian.be', 'password123', 'f', '2025-06-11 01:58:47.958483', '2025-06-21 07:06:13.314671');
INSERT INTO "public"."t_user" VALUES (1934460205056266240, 'testa', 't', NULL, 5008, 'z', '1', 'f', '2025-06-16 11:57:08.075555', '2025-06-21 07:06:15.757996');
INSERT INTO "public"."t_user" VALUES (1948197156565422080, 'guest', 't', NULL, 5004, 'guest', '25d55ad283aa400af464c76d713c07ad', 'f', '2025-07-24 09:42:52.535235', '2025-07-24 09:54:10.051293');
INSERT INTO "public"."t_user" VALUES (1948196919792766976, 'test', 't', NULL, 5004, 'test', '25d55ad283aa400af464c76d713c07ad', 'f', '2025-07-24 09:41:56.084712', '2025-07-24 09:54:14.565497');
INSERT INTO "public"."t_user" VALUES (1935937134821249024, '张茂才', 't', NULL, 5008, '789', '456', 't', '2025-06-20 13:45:55.571197', '2025-06-21 07:06:21.032509');
INSERT INTO "public"."t_user" VALUES (6012, '陈实习生', 't', 'chen_intern', 5013, '123', '123', 't', '2025-06-11 01:58:47.958483', '2025-06-21 07:06:33.677616');
INSERT INTO "public"."t_user" VALUES (6011, '冯离职员工', 't', 'feng_former', 5007, 'feng.former', 'password123', 't', '2025-06-11 01:58:47.958483', '2025-06-21 07:06:36.20478');
INSERT INTO "public"."t_user" VALUES (6008, '吴后端开发', 't', 'wu_be_dev', 5008, 'wu.be', 'password123', 'f', '2025-06-11 01:58:47.958483', '2025-06-21 07:06:46.758792');
INSERT INTO "public"."t_user" VALUES (6007, '周前端开发', 't', 'zhou_fe_dev', 5007, 'zhou.fe', 'password123', 'f', '2025-06-11 01:58:47.958483', '2025-06-21 07:06:48.397188');
INSERT INTO "public"."t_user" VALUES (6004, '赵前端经理', 't', 'zhao_fe_mgr', 5007, 'zhao.fe', 'password123', 't', '2025-06-11 01:58:47.958483', '2025-06-21 07:06:50.372243');
INSERT INTO "public"."t_user" VALUES (1948203299450785792, 'standardguest', 'f', NULL, 5004, 'standardguest', '25d55ad283aa400af464c76d713c07ad', 'f', '2025-07-24 10:07:17.113421', '2025-07-24 10:07:17.113425');
INSERT INTO "public"."t_user" VALUES (1938209346265681920, '徐国政', 'f', NULL, 5004, 'qwerty', 'e10adc3949ba59abbe56e057f20f883e', 'f', '2025-06-26 20:14:52.995278', '2025-07-02 04:24:26.322488');
INSERT INTO "public"."t_user" VALUES (1936613097838088192, 'ttt', 'f', NULL, 5007, 'abc', '900150983cd24fb0d6963f7d28e17f72', 'f', '2025-06-22 10:31:57.713584', '2025-07-02 04:24:26.80243');
INSERT INTO "public"."t_user" VALUES (1936631880975781888, 'a', 'f', NULL, 5009, 'aa', '0cc175b9c0f1b6a831c399e269772661', 'f', '2025-06-22 11:46:35.962764', '2025-07-02 04:24:27.01742');
INSERT INTO "public"."t_user" VALUES (1936613452760092672, 'abc', 'f', NULL, 5008, 'xxx', '9df62e693988eb4e1e1444ece0578579', 'f', '2025-06-22 10:33:22.333885', '2025-07-02 04:24:27.24232');
INSERT INTO "public"."t_user" VALUES (1936614969823072256, '555', 'f', NULL, 5009, 'ddd', '77963b7a931377ad4ab5ad6a9cd718aa', 't', '2025-06-22 10:39:24.029588', '2025-07-02 04:24:27.45234');
INSERT INTO "public"."t_user" VALUES (1948201542435868672, '标准维护人员', 'f', NULL, 5004, 'standardadmin', '579646aad11fae4dd295812fb4526245', 'f', '2025-07-24 10:00:18.208295', '2025-07-24 15:50:03.774449');
INSERT INTO "public"."t_user" VALUES (1948201366455455744, '标准审批人员', 'f', NULL, 5004, 'standardapprove', '579646aad11fae4dd295812fb4526245', 'f', '2025-07-24 09:59:36.251365', '2025-07-24 15:50:16.632445');
INSERT INTO "public"."t_user" VALUES (1948201235429593088, '标准设计人员', 'f', NULL, 5004, 'standarddesign', '579646aad11fae4dd295812fb4526245', 'f', '2025-07-24 09:59:05.01251', '2025-07-24 15:50:26.135597');
INSERT INTO "public"."t_user" VALUES (1940248407419523072, '256', 'f', NULL, 5008, '12314', '14e1b600b1fd579f47433b88e8d85291', 'f', '2025-07-02 11:17:23.058157', '2025-07-24 21:27:48.978363');
INSERT INTO "public"."t_user" VALUES (1939271549475491840, 'zsw', 'f', NULL, 5002, 'zsw', 'bed372cc00b0cf686847016882ca53b1', 'f', '2025-06-29 18:35:41.975539', '2025-07-02 14:16:06.775551');
INSERT INTO "public"."t_user" VALUES (1950373271077785600, '体系测试人员1', 'f', NULL, 5004, 'systemtest1', '5626cd8a27b2f4c9fc4347566bf6b571', 'f', '2025-07-30 09:49:58.665846', '2025-07-30 09:49:58.665857');
INSERT INTO "public"."t_user" VALUES (1950373802370273280, '体系测试人员2', 'f', NULL, 5004, 'systemtest2', '5626cd8a27b2f4c9fc4347566bf6b571', 'f', '2025-07-30 09:52:05.335504', '2025-07-30 09:52:05.33551');
INSERT INTO "public"."t_user" VALUES (1936006492402618368, '徐小青', 't', NULL, 5007, 'x2005', 'e10adc3949ba59abbe56e057f20f883e', 't', '2025-06-20 18:21:31.70736', '2025-06-21 07:06:22.911112');
INSERT INTO "public"."t_user" VALUES (1934845562293719040, '徐小青', 't', NULL, 5011, '徐小青', 'e10adc3949ba59abbe56e057f20f883e', 'f', '2025-06-17 13:28:24.400219', '2025-06-21 07:06:17.596095');
INSERT INTO "public"."t_user" VALUES (1935148278794555392, 'test', 't', NULL, 5007, 'test12345', 'e10adc3949ba59abbe56e057f20f883e', 't', '2025-06-18 09:31:17.638376', '2025-06-21 07:06:19.347844');
INSERT INTO "public"."t_user" VALUES (1950373941910573056, '体系测试人员3', 'f', NULL, 5004, 'systemtest3', '5626cd8a27b2f4c9fc4347566bf6b571', 'f', '2025-07-30 09:52:38.604994', '2025-07-30 09:52:38.605002');

-- ----------------------------
-- Indexes structure for table t_user
-- ----------------------------
CREATE INDEX "idx_user_organ_affiliation" ON "public"."t_user" USING btree (
  "organ_affiliation" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table t_user
-- ----------------------------
ALTER TABLE "public"."t_user" ADD CONSTRAINT "t_user_pk" PRIMARY KEY ("id");
