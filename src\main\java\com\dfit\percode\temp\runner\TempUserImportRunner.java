//package com.dfit.percode.temp.runner;
//
//import com.dfit.percode.Application;
//import com.dfit.percode.temp.userimport.service.IUserImportService;
//import org.springframework.boot.SpringApplication;
//import org.springframework.context.ConfigurableApplicationContext;
//import org.springframework.mock.web.MockMultipartFile;
//
//import java.io.File;
//import java.io.FileInputStream;
//
//public class TempUserImportRunner {
//
//    public static void main(String[] args) {
//        // We need to run the main application to get the context
//        ConfigurableApplicationContext context = SpringApplication.run(Application.class, args);
//        IUserImportService userImportService = context.getBean(IUserImportService.class);
//
//        try {
//            File file = new File("HR_USER.xlsx");
//            FileInputStream inputStream = new FileInputStream(file);
//            MockMultipartFile multipartFile = new MockMultipartFile("file", file.getName(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", inputStream);
//            String result = userImportService.importUsers(multipartFile);
//            System.out.println(result);
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
//            context.close();
//        }
//    }
//}