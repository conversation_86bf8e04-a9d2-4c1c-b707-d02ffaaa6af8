import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.*;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import org.w3c.dom.*;
import java.io.StringReader;
import javax.xml.parsers.ParserConfigurationException;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;

/**
 * ERP部门数据获取和对比工具
 * 获取ERP接口数据并转换为HR_DEPARTMENT格式进行对比
 */
public class ErpDepartmentFetcher {
    
    // 数据库连接配置
    private static final String DB_URL = "******************************************************";
    private static final String DB_USER = "postgres";
    private static final String DB_PASSWORD = "123456";
    
    // ERP接口配置
    private static final String ERP_URL = "https://dmzesb.nisco.cn/dmzesb/XYTQZSJ/MDM/services/GetDatasFromMDMQuery";
    
    public static void main(String[] args) {
        ErpDepartmentFetcher fetcher = new ErpDepartmentFetcher();
        try {
            System.out.println("=== ERP部门数据获取和对比工具 ===");
            
            // 1. 调用ERP接口获取数据
            String erpResponse = fetcher.callErpApi();
            if (erpResponse == null) {
                System.out.println("获取ERP数据失败");
                return;
            }
            
            // 2. 解析ERP响应数据
            List<Map<String, String>> erpDepartments = fetcher.parseErpResponse(erpResponse);
            System.out.println("ERP接口返回部门数量: " + erpDepartments.size());
            
            // 3. 转换为HR_DEPARTMENT格式
            List<HrDepartment> hrDepartments = fetcher.convertToHrFormat(erpDepartments);
            System.out.println("转换为HR格式部门数量: " + hrDepartments.size());
            
            // 4. 获取数据库中的部门数据
            List<HrDepartment> dbDepartments = fetcher.getDbDepartments();
            System.out.println("数据库中部门数量: " + dbDepartments.size());
            
            // 5. 对比数据
            fetcher.compareDepartments(hrDepartments, dbDepartments);
            
            // 6. 保存ERP数据到文件
            fetcher.saveErpDataToFile(hrDepartments);
            
        } catch (Exception e) {
            System.err.println("程序执行出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 调用ERP接口获取部门数据
     */
    private String callErpApi() {
        try {
            // 构建SOAP请求体
            String soapRequest = buildSoapRequest();
            
            // 创建HTTP连接
            URL url = new URL(ERP_URL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "text/xml; charset=utf-8");
            connection.setRequestProperty("SOAPAction", "http://tempuri.org/GetOrgInfoFromMDM");
            connection.setDoOutput(true);
            
            // 发送请求
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = soapRequest.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
            
            // 读取响应
            int responseCode = connection.getResponseCode();
            System.out.println("响应状态码: " + responseCode);
            
            StringBuilder response = new StringBuilder();
            try (BufferedReader br = new BufferedReader(new InputStreamReader(
                    responseCode == 200 ? connection.getInputStream() : connection.getErrorStream(),
                    StandardCharsets.UTF_8))) {
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
            }
            
            String responseText = response.toString();
            
            // 保存原始响应到文件
            try (FileWriter writer = new FileWriter("temp/erp_response.xml", StandardCharsets.UTF_8)) {
                writer.write(responseText);
            }
            System.out.println("原始响应已保存到: temp/erp_response.xml");
            
            return responseText;
            
        } catch (Exception e) {
            System.err.println("调用ERP接口失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 构建SOAP请求体
     */
    private String buildSoapRequest() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        String startDate = "2015/01/01 00:00:00";
        String endDate = sdf.format(new Date());
        
        return "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
               "<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">\n" +
               "  <soap:Header/>\n" +
               "  <soap:Body>\n" +
               "    <tem:GetOrgInfoFromMDM>\n" +
               "      <tem:InXml><![CDATA[<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
               "        <tem:GetOrgInfoFromMDM>\n" +
               "          <![CDATA[<UDEF1>" + startDate + "</UDEF1>\n" +
               "          <UDEF2>" + endDate + "</UDEF2>]]>\n" +
               "        </tem:GetOrgInfoFromMDM>]]>\n" +
               "      </tem:InXml>\n" +
               "    </tem:GetOrgInfoFromMDM>\n" +
               "  </soap:Body>\n" +
               "</soap:Envelope>";
    }
    
    /**
     * 解析ERP响应数据
     */
    private List<Map<String, String>> parseErpResponse(String responseText) {
        List<Map<String, String>> departments = new ArrayList<>();
        
        try {
            // 首先检查是否是JSON错误响应
            if (responseText.trim().startsWith("{")) {
                System.out.println("收到JSON响应，可能是错误信息:");
                System.out.println(responseText);
                return departments;
            }
            
            // 解析SOAP响应
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new InputSource(new StringReader(responseText)));
            
            // 查找GetOrgInfoFromMDMResult元素
            NodeList resultNodes = doc.getElementsByTagName("GetOrgInfoFromMDMResult");
            if (resultNodes.getLength() == 0) {
                System.out.println("未找到GetOrgInfoFromMDMResult元素");
                return departments;
            }
            
            String innerXml = resultNodes.item(0).getTextContent();
            if (innerXml == null || innerXml.trim().isEmpty()) {
                System.out.println("GetOrgInfoFromMDMResult为空");
                return departments;
            }
            
            System.out.println("提取的内部XML长度: " + innerXml.length());
            
            // 解析内部XML
            Document innerDoc = builder.parse(new InputSource(new StringReader(innerXml)));
            
            // 尝试多种可能的元素名称
            String[] possibleTags = {"DATA", "ITEM", "ROW", "RECORD"};
            
            for (String tag : possibleTags) {
                NodeList dataNodes = innerDoc.getElementsByTagName(tag);
                if (dataNodes.getLength() > 0) {
                    System.out.println("找到 " + dataNodes.getLength() + " 个 " + tag + " 元素");
                    
                    for (int i = 0; i < dataNodes.getLength(); i++) {
                        Node dataNode = dataNodes.item(i);
                        Map<String, String> dept = new HashMap<>();
                        
                        NodeList children = dataNode.getChildNodes();
                        for (int j = 0; j < children.getLength(); j++) {
                            Node child = children.item(j);
                            if (child.getNodeType() == Node.ELEMENT_NODE) {
                                String key = child.getNodeName();
                                String value = child.getTextContent();
                                dept.put(key, value != null ? value : "");
                            }
                        }
                        
                        if (!dept.isEmpty()) {
                            departments.add(dept);
                        }
                    }
                    break; // 找到数据就退出循环
                }
            }
            
            if (departments.isEmpty()) {
                System.out.println("未找到部门数据，尝试打印XML结构...");
                printXmlStructure(innerDoc.getDocumentElement(), 0, 3);
            }
            
        } catch (Exception e) {
            System.err.println("解析ERP响应失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return departments;
    }
    
    /**
     * 打印XML结构用于调试
     */
    private void printXmlStructure(Node node, int level, int maxLevel) {
        if (level > maxLevel) return;
        
        String indent = "  ".repeat(level);
        System.out.println(indent + node.getNodeName() + " (" + node.getNodeType() + ")");
        
        NodeList children = node.getChildNodes();
        for (int i = 0; i < Math.min(children.getLength(), 5); i++) {
            Node child = children.item(i);
            if (child.getNodeType() == Node.ELEMENT_NODE) {
                printXmlStructure(child, level + 1, maxLevel);
            }
        }
    }
    
    /**
     * 转换ERP数据为HR_DEPARTMENT格式
     */
    private List<HrDepartment> convertToHrFormat(List<Map<String, String>> erpDepartments) {
        List<HrDepartment> hrDepartments = new ArrayList<>();
        
        for (Map<String, String> erpDept : erpDepartments) {
            HrDepartment hrDept = new HrDepartment();
            
            // 映射字段 - 根据实际ERP字段调整
            hrDept.setOrgCode(erpDept.get("ORGCODE"));
            hrDept.setOrgName(erpDept.get("ORGNAME"));
            hrDept.setPreOrgCode(erpDept.get("PREORGCODE"));
            hrDept.setOrgLevel(erpDept.get("ORGLEVEL"));
            hrDept.setOrgType(erpDept.get("ORGTYPE"));
            hrDept.setOrgStatus(erpDept.get("ORGSTATUS"));
            hrDept.setCreateTime(erpDept.get("CREATETIME"));
            hrDept.setUpdateTime(erpDept.get("UPDATETIME"));
            
            hrDepartments.add(hrDept);
        }
        
        return hrDepartments;
    }
    
    /**
     * 获取数据库中的部门数据
     */
    private List<HrDepartment> getDbDepartments() {
        List<HrDepartment> departments = new ArrayList<>();
        
        String sql = "SELECT org_code, org_name, pre_org_code, org_level, org_type, org_status, " +
                    "create_time, update_time FROM hr_department ORDER BY org_code";
        
        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                HrDepartment dept = new HrDepartment();
                dept.setOrgCode(rs.getString("org_code"));
                dept.setOrgName(rs.getString("org_name"));
                dept.setPreOrgCode(rs.getString("pre_org_code"));
                dept.setOrgLevel(rs.getString("org_level"));
                dept.setOrgType(rs.getString("org_type"));
                dept.setOrgStatus(rs.getString("org_status"));
                dept.setCreateTime(rs.getString("create_time"));
                dept.setUpdateTime(rs.getString("update_time"));
                
                departments.add(dept);
            }
            
        } catch (SQLException e) {
            System.err.println("查询数据库失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return departments;
    }
    
    /**
     * 对比ERP数据和数据库数据
     */
    private void compareDepartments(List<HrDepartment> erpDepts, List<HrDepartment> dbDepts) {
        System.out.println("\n=== 数据对比结果 ===");
        
        // 创建映射便于查找
        Map<String, HrDepartment> erpMap = new HashMap<>();
        Map<String, HrDepartment> dbMap = new HashMap<>();
        
        for (HrDepartment dept : erpDepts) {
            if (dept.getOrgCode() != null) {
                erpMap.put(dept.getOrgCode(), dept);
            }
        }
        
        for (HrDepartment dept : dbDepts) {
            if (dept.getOrgCode() != null) {
                dbMap.put(dept.getOrgCode(), dept);
            }
        }
        
        // 统计差异
        Set<String> erpOnly = new HashSet<>(erpMap.keySet());
        erpOnly.removeAll(dbMap.keySet());
        
        Set<String> dbOnly = new HashSet<>(dbMap.keySet());
        dbOnly.removeAll(erpMap.keySet());
        
        Set<String> common = new HashSet<>(erpMap.keySet());
        common.retainAll(dbMap.keySet());
        
        System.out.println("ERP独有部门数量: " + erpOnly.size());
        System.out.println("数据库独有部门数量: " + dbOnly.size());
        System.out.println("共同部门数量: " + common.size());
        
        // 显示差异详情
        if (!erpOnly.isEmpty()) {
            System.out.println("\nERP独有部门 (前10个):");
            erpOnly.stream().limit(10).forEach(code -> {
                HrDepartment dept = erpMap.get(code);
                System.out.println("  " + code + " - " + dept.getOrgName());
            });
        }
        
        if (!dbOnly.isEmpty()) {
            System.out.println("\n数据库独有部门 (前10个):");
            dbOnly.stream().limit(10).forEach(code -> {
                HrDepartment dept = dbMap.get(code);
                System.out.println("  " + code + " - " + dept.getOrgName());
            });
        }
        
        // 检查共同部门的字段差异
        int diffCount = 0;
        for (String code : common) {
            HrDepartment erpDept = erpMap.get(code);
            HrDepartment dbDept = dbMap.get(code);
            
            if (!Objects.equals(erpDept.getOrgName(), dbDept.getOrgName()) ||
                !Objects.equals(erpDept.getPreOrgCode(), dbDept.getPreOrgCode()) ||
                !Objects.equals(erpDept.getOrgLevel(), dbDept.getOrgLevel())) {
                diffCount++;
                if (diffCount <= 5) { // 只显示前5个差异
                    System.out.println("\n字段差异 - " + code + ":");
                    System.out.println("  ERP: " + erpDept.getOrgName() + " | DB: " + dbDept.getOrgName());
                }
            }
        }
        
        if (diffCount > 0) {
            System.out.println("共有 " + diffCount + " 个部门存在字段差异");
        }
    }
    
    /**
     * 保存ERP数据到文件
     */
    private void saveErpDataToFile(List<HrDepartment> departments) {
        try (FileWriter writer = new FileWriter("temp/erp_departments.csv", StandardCharsets.UTF_8)) {
            // 写入CSV头
            writer.write("org_code,org_name,pre_org_code,org_level,org_type,org_status,create_time,update_time\n");
            
            // 写入数据
            for (HrDepartment dept : departments) {
                writer.write(String.format("%s,%s,%s,%s,%s,%s,%s,%s\n",
                    csvEscape(dept.getOrgCode()),
                    csvEscape(dept.getOrgName()),
                    csvEscape(dept.getPreOrgCode()),
                    csvEscape(dept.getOrgLevel()),
                    csvEscape(dept.getOrgType()),
                    csvEscape(dept.getOrgStatus()),
                    csvEscape(dept.getCreateTime()),
                    csvEscape(dept.getUpdateTime())
                ));
            }
            
            System.out.println("ERP部门数据已保存到: temp/erp_departments.csv");
            
        } catch (IOException e) {
            System.err.println("保存文件失败: " + e.getMessage());
        }
    }
    
    private String csvEscape(String value) {
        if (value == null) return "";
        if (value.contains(",") || value.contains("\"") || value.contains("\n")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }
        return value;
    }
    
    /**
     * HR部门数据模型
     */
    static class HrDepartment {
        private String orgCode;
        private String orgName;
        private String preOrgCode;
        private String orgLevel;
        private String orgType;
        private String orgStatus;
        private String createTime;
        private String updateTime;
        
        // Getters and Setters
        public String getOrgCode() { return orgCode; }
        public void setOrgCode(String orgCode) { this.orgCode = orgCode; }
        
        public String getOrgName() { return orgName; }
        public void setOrgName(String orgName) { this.orgName = orgName; }
        
        public String getPreOrgCode() { return preOrgCode; }
        public void setPreOrgCode(String preOrgCode) { this.preOrgCode = preOrgCode; }
        
        public String getOrgLevel() { return orgLevel; }
        public void setOrgLevel(String orgLevel) { this.orgLevel = orgLevel; }
        
        public String getOrgType() { return orgType; }
        public void setOrgType(String orgType) { this.orgType = orgType; }
        
        public String getOrgStatus() { return orgStatus; }
        public void setOrgStatus(String orgStatus) { this.orgStatus = orgStatus; }
        
        public String getCreateTime() { return createTime; }
        public void setCreateTime(String createTime) { this.createTime = createTime; }
        
        public String getUpdateTime() { return updateTime; }
        public void setUpdateTime(String updateTime) { this.updateTime = updateTime; }
    }
}
