#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于工号的用户部门验证工具（修正版）
通过工号(account)匹配，避免重名问题
"""

import csv
import re
from collections import defaultdict

def parse_sql_insert_statements(sql_file):
    """解析SQL文件中的INSERT语句，获取用户数据"""
    users = {}  # account -> user_info
    
    try:
        with open(sql_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 查找INSERT语句的模式
        insert_pattern = r"INSERT INTO.*?t_user.*?VALUES\s*\((.*?)\);"
        matches = re.findall(insert_pattern, content, re.DOTALL | re.IGNORECASE)
        
        for match in matches:
            # 简单解析VALUES中的数据
            values = []
            parts = match.split(',')
            for part in parts:
                part = part.strip().strip("'\"")
                values.append(part)
            
            if len(values) >= 10:  # 确保有足够的字段
                # 实际字段顺序: id, user_name, is_del, origin_id, organ_affiliation, account, password, is_del, create_time, modify_time
                user_info = {
                    'id': values[0],
                    'user_name': values[1],
                    'is_del': values[2],
                    'origin_id': values[3] if values[3] != 'NULL' else None,
                    'organ_affiliation': values[4] if values[4] != 'NULL' else None,
                    'account': values[5],
                    'password': values[6] if len(values) > 6 else None,
                    'is_del2': values[7] if len(values) > 7 else None,
                    'create_time': values[8] if len(values) > 8 else None,
                    'modify_time': values[9] if len(values) > 9 else None,
                    'phone': None  # 这个字段在SQL中不存在
                }

                # 只保留未删除的用户
                if values[2].lower() in ['f', 'false']:
                    users[values[5]] = user_info
                
    except Exception as e:
        print(f"解析用户SQL文件时出错: {e}")
        return {}
    
    return users

def parse_org_sql_statements(sql_file):
    """解析组织结构SQL文件"""
    orgs = {}  # id -> org_info
    
    try:
        with open(sql_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        insert_pattern = r"INSERT INTO.*?t_org_structure.*?VALUES\s*\((.*?)\);"
        matches = re.findall(insert_pattern, content, re.DOTALL | re.IGNORECASE)
        
        for match in matches:
            values = []
            parts = match.split(',')
            for part in parts:
                part = part.strip().strip("'\"")
                values.append(part)
            
            if len(values) >= 8:
                org_info = {
                    'id': values[0],
                    'organ_name': values[1],
                    'pre_id': values[2] if values[2] != 'NULL' else None,
                    'order_info': values[3] if values[3] != 'NULL' else None,
                    'is_del': values[4],
                }
                
                # 只保留未删除的组织
                if values[4].lower() in ['f', 'false']:
                    orgs[values[0]] = org_info
                
    except Exception as e:
        print(f"解析组织SQL文件时出错: {e}")
        return {}
    
    return orgs

def load_hr_mapping():
    """加载HR系统的用户部门对应关系"""
    hr_mapping = {}  # account -> hr_info
    
    with open('用户部门对应关系.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for row in reader:
            account = row['用户工号'].strip()
            if account:  # 只处理有工号的记录
                hr_mapping[account] = {
                    'name': row['人员姓名'].strip(),
                    'dept_name': row['部门名称'].strip(),
                    'phone': row['手机号码'].strip(),
                    'dept_id': row['部门ID'].strip()
                }
    
    return hr_mapping

def validate_users(db_users, db_orgs, hr_mapping):
    """验证用户数据"""
    results = []
    
    # 统计信息
    stats = {
        'total_hr_users': len(hr_mapping),
        'total_db_users': len(db_users),
        'perfect_match': 0,
        'name_mismatch': 0,
        'dept_mismatch': 0,
        'no_org_in_db': 0,
        'not_in_db': 0,
        'not_in_hr': 0
    }
    
    print("开始验证用户数据...")
    
    # 1. 检查HR系统中的用户在权限系统中的情况
    for account, hr_info in hr_mapping.items():
        if account in db_users:
            db_user = db_users[account]
            
            # 检查姓名是否匹配
            name_match = hr_info['name'] == db_user['user_name']
            
            # 检查部门关联
            if db_user['organ_affiliation']:
                if db_user['organ_affiliation'] in db_orgs:
                    db_dept_name = db_orgs[db_user['organ_affiliation']]['organ_name']
                    dept_match = hr_info['dept_name'] == db_dept_name
                    
                    if name_match and dept_match:
                        status = 'perfect_match'
                        stats['perfect_match'] += 1
                    elif not name_match:
                        status = 'name_mismatch'
                        stats['name_mismatch'] += 1
                    else:
                        status = 'dept_mismatch'
                        stats['dept_mismatch'] += 1
                    
                    results.append({
                        'account': account,
                        'hr_name': hr_info['name'],
                        'db_name': db_user['user_name'],
                        'hr_dept': hr_info['dept_name'],
                        'db_dept': db_dept_name,
                        'hr_phone': hr_info['phone'],
                        'db_phone': db_user['phone'] or '',
                        'status': status
                    })
                else:
                    # 数据库中的组织ID不存在
                    status = 'dept_mismatch'
                    stats['dept_mismatch'] += 1
                    results.append({
                        'account': account,
                        'hr_name': hr_info['name'],
                        'db_name': db_user['user_name'],
                        'hr_dept': hr_info['dept_name'],
                        'db_dept': f"组织ID不存在({db_user['organ_affiliation']})",
                        'hr_phone': hr_info['phone'],
                        'db_phone': db_user['phone'] or '',
                        'status': status
                    })
            else:
                # 数据库中用户没有部门关联
                status = 'no_org_in_db'
                stats['no_org_in_db'] += 1
                results.append({
                    'account': account,
                    'hr_name': hr_info['name'],
                    'db_name': db_user['user_name'],
                    'hr_dept': hr_info['dept_name'],
                    'db_dept': '无部门关联',
                    'hr_phone': hr_info['phone'],
                    'db_phone': db_user['phone'] or '',
                    'status': status
                })
        else:
            # HR系统中的用户在权限系统中不存在
            status = 'not_in_db'
            stats['not_in_db'] += 1
            results.append({
                'account': account,
                'hr_name': hr_info['name'],
                'db_name': '用户不存在',
                'hr_dept': hr_info['dept_name'],
                'db_dept': '用户不存在',
                'hr_phone': hr_info['phone'],
                'db_phone': '',
                'status': status
            })
    
    # 2. 检查权限系统中存在但HR系统中不存在的用户
    for account, db_user in db_users.items():
        if account not in hr_mapping:
            status = 'not_in_hr'
            stats['not_in_hr'] += 1
            
            db_dept_name = ''
            if db_user['organ_affiliation'] and db_user['organ_affiliation'] in db_orgs:
                db_dept_name = db_orgs[db_user['organ_affiliation']]['organ_name']
            
            results.append({
                'account': account,
                'hr_name': '用户不存在',
                'db_name': db_user['user_name'],
                'hr_dept': '用户不存在',
                'db_dept': db_dept_name or '无部门关联',
                'hr_phone': '',
                'db_phone': db_user['phone'] or '',
                'status': status
            })
    
    return results, stats

def save_results(results, stats):
    """保存验证结果"""
    output_file = '基于工号的验证结果_修正版.csv'
    
    with open(output_file, 'w', encoding='utf-8', newline='') as f:
        writer = csv.writer(f)
        
        # 写入标题行
        writer.writerow([
            '工号', 'HR姓名', '权限系统姓名', 'HR部门', '权限系统部门', 
            'HR手机', '权限系统手机', '验证状态'
        ])
        
        # 写入数据
        for result in results:
            writer.writerow([
                result['account'],
                result['hr_name'],
                result['db_name'],
                result['hr_dept'],
                result['db_dept'],
                result['hr_phone'],
                result['db_phone'],
                result['status']
            ])
    
    print(f"\n验证结果已保存到: {output_file}")
    
    # 输出统计信息
    print(f"\n=== 验证统计 ===")
    print(f"HR系统用户总数: {stats['total_hr_users']}")
    print(f"权限系统用户总数: {stats['total_db_users']}")
    print(f"完全匹配: {stats['perfect_match']}")
    print(f"姓名不匹配: {stats['name_mismatch']}")
    print(f"部门不匹配: {stats['dept_mismatch']}")
    print(f"权限系统无部门: {stats['no_org_in_db']}")
    print(f"权限系统中不存在: {stats['not_in_db']}")
    print(f"HR系统中不存在: {stats['not_in_hr']}")
    
    # 计算匹配率
    total_common = stats['perfect_match'] + stats['name_mismatch'] + stats['dept_mismatch'] + stats['no_org_in_db']
    if total_common > 0:
        match_rate = stats['perfect_match'] / total_common * 100
        print(f"完全匹配率: {match_rate:.1f}%")

def main():
    """主函数"""
    print("=== 基于工号的用户部门验证工具（修正版） ===")
    
    # 1. 解析权限系统数据
    print("解析权限系统用户数据...")
    db_users = parse_sql_insert_statements("t_user.sql")
    print(f"权限系统用户数: {len(db_users)}")
    
    print("解析权限系统组织数据...")
    db_orgs = parse_org_sql_statements("t_org_structure.sql")
    print(f"权限系统组织数: {len(db_orgs)}")
    
    # 2. 加载HR系统数据
    print("加载HR系统用户部门对应关系...")
    hr_mapping = load_hr_mapping()
    print(f"HR系统用户数: {len(hr_mapping)}")
    
    # 3. 执行验证
    results, stats = validate_users(db_users, db_orgs, hr_mapping)
    
    # 4. 保存结果
    save_results(results, stats)
    
    print("\n=== 验证完成 ===")

if __name__ == "__main__":
    main()
