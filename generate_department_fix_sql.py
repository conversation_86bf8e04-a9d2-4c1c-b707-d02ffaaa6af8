#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成部门关联修正SQL脚本
分析不匹配的用户，生成相应的UPDATE语句来修正数据库中的部门关联
"""

import csv
import uuid
from collections import defaultdict, Counter

def load_validation_results():
    """加载验证结果"""
    no_org_users = []
    dept_mismatch_users = []
    
    with open('基于工号的验证结果.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for row in reader:
            if row['验证状态'] == 'no_org_in_db':
                no_org_users.append(row)
            elif row['验证状态'] == 'dept_mismatch':
                dept_mismatch_users.append(row)
    
    return no_org_users, dept_mismatch_users

def analyze_department_patterns(no_org_users):
    """分析部门模式"""
    print("=== 分析部门模式 ===")
    
    # 统计部门ID模式
    dept_id_patterns = Counter()
    unknown_dept_count = 0
    known_dept_count = 0
    
    dept_id_to_names = defaultdict(set)
    
    for user in no_org_users:
        dept_name = user['CSV部门名']
        dept_id = user['CSV部门ID']
        
        if '未知部门' in dept_name:
            unknown_dept_count += 1
            # 提取部门ID模式
            if dept_id:
                prefix = dept_id[:2] if len(dept_id) >= 2 else dept_id
                dept_id_patterns[prefix] += 1
        else:
            known_dept_count += 1
            dept_id_to_names[dept_id].add(dept_name)
    
    print(f"未知部门用户: {unknown_dept_count}")
    print(f"已知部门用户: {known_dept_count}")
    
    print(f"\n部门ID前缀统计 (前10个):")
    for prefix, count in dept_id_patterns.most_common(10):
        print(f"  {prefix}*: {count}个用户")
    
    print(f"\n已知部门名称统计:")
    for dept_id, names in dept_id_to_names.items():
        if len(names) == 1:
            print(f"  {list(names)[0]}: {dept_id}")
        else:
            print(f"  多个名称 {list(names)}: {dept_id}")
    
    return dept_id_patterns, dept_id_to_names

def generate_department_creation_sql(dept_id_to_names):
    """生成部门创建SQL"""
    print("\n=== 生成部门创建SQL ===")
    
    sql_statements = []
    sql_statements.append("-- 创建缺失的部门组织")
    sql_statements.append("-- 注意: 执行前请确认这些部门确实需要在权限系统中创建")
    sql_statements.append("")
    
    for dept_id, names in dept_id_to_names.items():
        if dept_id and names:
            dept_name = list(names)[0]  # 使用第一个名称
            org_uuid = str(uuid.uuid4())
            
            sql = f"""INSERT INTO t_org_structure (id, organ_name, pre_id, organ_code, is_del, create_time, update_time) 
VALUES ('{org_uuid}', '{dept_name}', NULL, '{dept_id}', false, NOW(), NOW());"""
            
            sql_statements.append(sql)
            sql_statements.append(f"-- 部门: {dept_name}, 原始ID: {dept_id}")
            sql_statements.append("")
    
    return sql_statements, dept_id_to_names

def generate_user_update_sql(no_org_users, dept_id_to_names):
    """生成用户部门关联更新SQL"""
    print("=== 生成用户更新SQL ===")
    
    sql_statements = []
    sql_statements.append("-- 更新用户的部门关联")
    sql_statements.append("-- 注意: 执行前请确认部门映射关系正确")
    sql_statements.append("")
    
    # 为已知部门的用户生成更新语句
    known_dept_updates = []
    unknown_dept_updates = []
    
    for user in no_org_users:
        account = user['CSV工号']
        user_name = user['CSV用户名']
        dept_name = user['CSV部门名']
        dept_id = user['CSV部门ID']
        
        if '未知部门' not in dept_name and dept_id in dept_id_to_names:
            # 已知部门，可以直接更新
            sql = f"""UPDATE t_user SET organ_affiliation = (
    SELECT id FROM t_org_structure WHERE organ_code = '{dept_id}' AND is_del = false LIMIT 1
) WHERE account = '{account}' AND is_del = false;"""
            
            known_dept_updates.append({
                'sql': sql,
                'comment': f"-- 用户: {user_name} ({account}) -> {dept_name}"
            })
        else:
            # 未知部门，需要手动处理
            unknown_dept_updates.append({
                'account': account,
                'user_name': user_name,
                'dept_name': dept_name,
                'dept_id': dept_id
            })
    
    # 添加已知部门的更新语句
    if known_dept_updates:
        sql_statements.append("-- 更新已知部门的用户")
        for update in known_dept_updates:
            sql_statements.append(update['comment'])
            sql_statements.append(update['sql'])
            sql_statements.append("")
    
    # 添加未知部门的注释
    if unknown_dept_updates:
        sql_statements.append("-- 以下用户的部门需要手动处理 (未知部门)")
        sql_statements.append("-- 建议: 1. 确认部门是否需要创建 2. 建立部门ID映射关系 3. 手动更新")
        for update in unknown_dept_updates[:20]:  # 只显示前20个
            sql_statements.append(f"-- {update['user_name']} ({update['account']}) -> {update['dept_name']} (ID: {update['dept_id']})")
        
        if len(unknown_dept_updates) > 20:
            sql_statements.append(f"-- ... 还有 {len(unknown_dept_updates) - 20} 个用户需要处理")
    
    return sql_statements, len(known_dept_updates), len(unknown_dept_updates)

def generate_verification_sql():
    """生成验证SQL"""
    sql_statements = []
    sql_statements.append("-- 验证SQL - 检查修正结果")
    sql_statements.append("")
    sql_statements.append("-- 1. 检查仍然没有部门关联的用户数量")
    sql_statements.append("SELECT COUNT(*) as users_without_dept FROM t_user WHERE organ_affiliation IS NULL AND is_del = false;")
    sql_statements.append("")
    sql_statements.append("-- 2. 检查新创建的部门数量")
    sql_statements.append("SELECT COUNT(*) as new_departments FROM t_org_structure WHERE organ_code IS NOT NULL AND is_del = false;")
    sql_statements.append("")
    sql_statements.append("-- 3. 检查部门关联统计")
    sql_statements.append("""SELECT 
    CASE 
        WHEN organ_affiliation IS NULL THEN '无部门'
        ELSE '有部门'
    END as dept_status,
    COUNT(*) as user_count
FROM t_user 
WHERE is_del = false 
GROUP BY (organ_affiliation IS NULL);""")
    
    return sql_statements

def main():
    """主函数"""
    print("=== 部门关联修正SQL生成工具 ===")
    
    # 加载验证结果
    no_org_users, dept_mismatch_users = load_validation_results()
    
    print(f"需要处理的用户:")
    print(f"  数据库无部门: {len(no_org_users)}")
    print(f"  部门不匹配: {len(dept_mismatch_users)}")
    
    # 分析部门模式
    dept_id_patterns, dept_id_to_names = analyze_department_patterns(no_org_users)
    
    # 生成SQL文件
    all_sql = []
    
    # 1. 生成部门创建SQL
    dept_creation_sql, dept_mapping = generate_department_creation_sql(dept_id_to_names)
    all_sql.extend(dept_creation_sql)
    
    # 2. 生成用户更新SQL
    user_update_sql, known_count, unknown_count = generate_user_update_sql(no_org_users, dept_mapping)
    all_sql.extend(user_update_sql)
    
    # 3. 生成验证SQL
    verification_sql = generate_verification_sql()
    all_sql.extend(verification_sql)
    
    # 保存SQL文件
    output_file = "department_fix.sql"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(all_sql))
    
    print(f"\n=== 生成完成 ===")
    print(f"SQL文件已保存: {output_file}")
    print(f"需要创建的部门: {len(dept_mapping)}")
    print(f"可直接更新的用户: {known_count}")
    print(f"需要手动处理的用户: {unknown_count}")
    
    print(f"\n=== 执行建议 ===")
    print("1. 仔细审查生成的SQL文件")
    print("2. 在测试环境中先执行验证")
    print("3. 确认部门创建的必要性")
    print("4. 分批执行，先创建部门，再更新用户")
    print("5. 执行后运行验证SQL检查结果")

if __name__ == "__main__":
    main()
