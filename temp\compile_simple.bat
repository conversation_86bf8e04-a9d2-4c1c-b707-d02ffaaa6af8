@echo off
echo === 简化版ERP部门数据获取工具 ===

REM 设置编码
chcp 65001

REM 检查Java环境
echo 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请确保已安装JDK
    pause
    exit /b 1
)

REM 编译Java程序
echo.
echo 编译Java程序...
javac -encoding UTF-8 SimpleErpFetcher.java
if %errorlevel% neq 0 (
    echo 错误: 编译失败
    pause
    exit /b 1
)

echo 编译成功！

REM 运行程序
echo.
echo 运行程序...
java -Dfile.encoding=UTF-8 SimpleErpFetcher

echo.
echo 程序执行完成！
echo 生成的文件:
echo   - erp_response.xml (原始响应)
echo   - erp_departments_raw.csv (原始数据)
echo   - erp_departments_hr_format.csv (HR格式数据)
pause
